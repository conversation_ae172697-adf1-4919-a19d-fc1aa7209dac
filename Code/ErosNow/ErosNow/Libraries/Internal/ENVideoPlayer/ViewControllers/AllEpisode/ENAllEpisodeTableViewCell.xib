<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="23504" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="23506"/>
        <capability name="Named colors" minToolsVersion="9.0"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="none" indentationWidth="10" reuseIdentifier="ENAllEpisodeTableViewCell" rowHeight="76" id="KGk-i7-Jjw" customClass="ENAllEpisodeTableViewCell" customModule="ErosNow" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="470" height="100"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="470" height="100"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="MLc-1h-6dp">
                        <rect key="frame" x="2" y="2" width="128" height="72"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="width" secondItem="MLc-1h-6dp" secondAttribute="height" multiplier="16:9" id="pUa-xn-xjp"/>
                        </constraints>
                    </imageView>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Episode 1  •  45 min" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="6nf-gS-fmW">
                        <rect key="frame" x="146" y="4" width="113" height="15"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="uDQ-gH-CSX"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" white="1" alpha="0.60096750827814571" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="The Hunt" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="s0s-ZL-jM2">
                        <rect key="frame" x="146" y="23" width="69.666666666666686" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="U7O-Al-3Y1"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" weight="semibold" pointSize="16"/>
                        <color key="textColor" name="titleColor"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yBn-HN-eOo">
                        <rect key="frame" x="146" y="47" width="320" height="30"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="30" id="OrK-nk-dXM"/>
                        </constraints>
                        <string key="text">A woman finds herself in a forest along with others where they strive to escape and survive the human predator out to hunt them down.</string>
                        <fontDescription key="fontDescription" type="system" pointSize="12"/>
                        <color key="textColor" white="1" alpha="0.60096750830000001" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text=" NOW WATCHING " textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BKw-8C-T1V" userLabel="Now Playing">
                        <rect key="frame" x="267" y="4" width="88" height="15"/>
                        <color key="backgroundColor" systemColor="tintColor"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="15" id="k8e-EC-QRo"/>
                        </constraints>
                        <fontDescription key="fontDescription" type="system" pointSize="10"/>
                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="BKw-8C-T1V" secondAttribute="trailing" constant="16" id="2Lc-qo-tlB"/>
                    <constraint firstItem="s0s-ZL-jM2" firstAttribute="top" secondItem="6nf-gS-fmW" secondAttribute="bottom" constant="4" id="4JF-sK-Q8i"/>
                    <constraint firstItem="yBn-HN-eOo" firstAttribute="leading" secondItem="MLc-1h-6dp" secondAttribute="trailing" constant="16" id="5Tr-DL-L4c"/>
                    <constraint firstItem="6nf-gS-fmW" firstAttribute="leading" secondItem="MLc-1h-6dp" secondAttribute="trailing" constant="16" id="Jpp-kA-d42"/>
                    <constraint firstItem="yBn-HN-eOo" firstAttribute="top" secondItem="s0s-ZL-jM2" secondAttribute="bottom" constant="4" id="Jxh-Ry-He3"/>
                    <constraint firstItem="s0s-ZL-jM2" firstAttribute="leading" secondItem="MLc-1h-6dp" secondAttribute="trailing" constant="16" id="VLU-jL-Yba"/>
                    <constraint firstAttribute="trailing" secondItem="yBn-HN-eOo" secondAttribute="trailing" constant="4" id="Ykk-rL-7Hi"/>
                    <constraint firstItem="BKw-8C-T1V" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="4" id="ZpY-xc-9Zm"/>
                    <constraint firstItem="6nf-gS-fmW" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="4" id="cDg-LL-8Ux"/>
                    <constraint firstAttribute="bottom" secondItem="MLc-1h-6dp" secondAttribute="bottom" constant="26" id="eKL-BP-vag"/>
                    <constraint firstItem="MLc-1h-6dp" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="2" id="n5i-LO-lJh"/>
                    <constraint firstItem="BKw-8C-T1V" firstAttribute="leading" secondItem="6nf-gS-fmW" secondAttribute="trailing" constant="8" id="tVQ-ZY-HAc"/>
                    <constraint firstItem="MLc-1h-6dp" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="2" id="yeR-sv-vbl"/>
                </constraints>
            </tableViewCellContentView>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="episodeDesc" destination="yBn-HN-eOo" id="4cb-cs-u3P"/>
                <outlet property="episodeTitle" destination="s0s-ZL-jM2" id="eVH-lP-5LP"/>
                <outlet property="nowPlayingLabel" destination="BKw-8C-T1V" id="FYc-LP-iw3"/>
                <outlet property="sessionTitle" destination="6nf-gS-fmW" id="O1Q-ee-20m"/>
                <outlet property="videoImage" destination="MLc-1h-6dp" id="LkU-E5-cDj"/>
            </connections>
            <point key="canvasLocation" x="198.47328244274809" y="-15.492957746478874"/>
        </tableViewCell>
    </objects>
    <resources>
        <namedColor name="titleColor">
            <color red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </namedColor>
        <systemColor name="tintColor">
            <color red="0.0" green="0.47843137250000001" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
