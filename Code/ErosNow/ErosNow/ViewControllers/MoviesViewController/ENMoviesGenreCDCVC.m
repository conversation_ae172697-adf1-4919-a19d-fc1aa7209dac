//
//  ENMoviesGenreCDCVC.m
//  ErosNow
//
//  Created by <PERSON><PERSON> on 22/01/15.
//  Copyright (c) 2015 ErosNow. All rights reserved.
//

#import "ENMoviesGenreCDCVC.h"
#import "ENMovieMaster+Fetching.h"
#import "ENDataManager.h"
#import "ENMovieCollectionViewCell.h"
#import "ENMovieDetail.h"
#import <SDWebImage/UIImageView+WebCache.h>
#import "ENUtilityVC.h"
#import "ENGenre.h"
#import "ENAppSetting.h"
#import "ENMovieLanguageListViewController.h"
#import "ENLanguage+Additions.h"
#import "UIFont+WhiteSkin.h"
#import "UIColor+WhiteSkin.h"

#define ORIGIN_Y_PADDING 3
#define ORIGIN_X_PADDING 0
#define WIDTH_PADDING 21

@interface ENMoviesGenreCDCVC ()<ENLanguageListViewControllerDelegate> {
    
}
@property (nonatomic, copy) NSString                     *emptyMessage;
@property (weak, nonatomic)NSDictionary *languageDict;
@property (nonatomic, copy)NSString *languages;


@end


@implementation ENMoviesGenreCDCVC

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self p_addObservers];
    [self en_setBlackBackground];
    self.collectionView.backgroundColor = [UIColor en_blackBackground
                                           ];
    self.title  = [self.genreObject.type uppercaseString];
    self.movieMaster = [ENMovieMaster masterObjectForId:self.genreObject.genreId title:self.genreObject.type];
//    [self getLanguages];
    [self p__initialSetup];
}

- (void)dealloc
{
    [self p_removeObservers];
}
- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    [ENUtility postGoogleScreenName:@"Movie_Genre_List_Screen"];
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

#pragma mark - Abstract Methods

- (ENCollectionViewSectionType)en_sectionTypeForSectionIndex:(NSInteger)sectionIndex {
    return ENCollectionViewSectionTypeContent;
}

#pragma mark - <UICollectionViewDelegate>
- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    NSArray *sections = [self.fetchedResultsController sections];
    id<NSFetchedResultsSectionInfo> sectionInfo = [sections objectAtIndex:section];
    NSInteger rowCount = [sectionInfo numberOfObjects];
    if (rowCount == 0 && self.emptyMessage) {
        [self p_showEmptyLabelWithText:self.emptyMessage];
    }
    else {
        [self p_showEmptyLabelWithText:@""];
    }
    return rowCount;
}


#pragma mark - Private Methods

- (void)p__initialSetup {
    
    [self p_setUpFetchedResultsController];
    [self en_pullToRefreshFromServer];
}

- (NSDictionary *)p_parseUserLanguagesData:(NSDictionary *)responseDict ForLanguageMaster: (ENLanguageMaster *)languageMaster{
    
    NSDictionary *rootArray = [[[responseDict objectForKey:@"movie"] objectForKey:EN_LANGUAGE_KEY] objectForKey:EN_USER_LANGUAGE_VALUE];
    if([rootArray count] == 0 || rootArray == nil){
        rootArray = [[[responseDict objectForKey:@"movie"] objectForKey:EN_LANGUAGE_KEY] objectForKey:@"all"];
    }
    NSLog(@"root array %@",rootArray);

    return rootArray;
}

- (void)p_setUpFetchedResultsController {
    NSFetchRequest *fetchRequest = [[NSFetchRequest alloc] init];
    NSEntityDescription *entity = [NSEntityDescription entityForName:@"ENMovieDetail" inManagedObjectContext:[ENDataManager sharedInstance].managedObjectContext];
    [fetchRequest setEntity:entity];
    //TODO: to move to modal Class
    
    NSArray *languages = [self.selectedLanguage componentsSeparatedByString:@","];
    NSArray *subPredicates = [[NSArray alloc] init];
    NSMutableArray *subPredicatesAux = [[NSMutableArray alloc] init];
    NSPredicate *predicate;
    
    for( int i=0; i<languages.count; i++ )
    {
        predicate = [NSPredicate predicateWithFormat:@"languageKey = %@", languages[i]];
        [subPredicatesAux addObject:predicate];
    }
    
    subPredicates = [subPredicatesAux copy];
    
    NSPredicate *compoundPredicate = [NSCompoundPredicate orPredicateWithSubpredicates:subPredicates];
    NSPredicate *firstpredicate = [NSPredicate predicateWithFormat:@"self.movieMaster = %@", self.movieMaster];
    
    fetchRequest.predicate = [NSCompoundPredicate andPredicateWithSubpredicates:@[compoundPredicate, firstpredicate]];
    
    NSSortDescriptor *assetSortDescriptor    = [[NSSortDescriptor alloc] initWithKey:@"assetIndex" ascending:YES];
    NSArray *sortDescriptors = @[assetSortDescriptor];
    // [fetchRequest setFetchLimit:10];
    [fetchRequest setSortDescriptors:sortDescriptors];
    
    
    NSFetchedResultsController *aFetchedResultsController = [[NSFetchedResultsController alloc] initWithFetchRequest:fetchRequest managedObjectContext:[ENDataManager sharedInstance].managedObjectContext sectionNameKeyPath:nil cacheName:nil];
    self.fetchedResultsController = aFetchedResultsController;
    
    NSError *error = nil;
    if (![self.fetchedResultsController performFetch:&error]) {
        // Replace this implementation with code to handle the error appropriately.
        // abort() causes the application to generate a crash log and terminate. You should not use this function in a shipping application, although it may be useful during development.
        NSLog(@"Unresolved error %@, %@", error, [error userInfo]);
        abort();
    }
    else
    {
    }
}

- (void)p_alertMessageSetup {
    NSArray *sections = [self.fetchedResultsController sections];
    id<NSFetchedResultsSectionInfo> sectionInfo = [sections objectAtIndex:0];
    NSInteger rowCount = [sectionInfo numberOfObjects];
    if (rowCount == 0) {
        self.emptyMessage = @"This genre has no movies";
        [self.collectionView reloadData];
    }
    [super languageFilterViewSetup];
}

- (void)p_showEmptyLabelWithText:(NSString *)text {
    if (self.collectionView.backgroundView) {
        UILabel *messageLabel = (UILabel *)self.collectionView.backgroundView;
        messageLabel.text = text;
    } else {
        UILabel *messageLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, self.view.bounds.size.width, self.view.bounds.size.height)];
        messageLabel.text = self.emptyMessage;
        messageLabel.textColor = [UIColor en_secondaryText];
        messageLabel.numberOfLines = 0;
        messageLabel.textAlignment = NSTextAlignmentCenter;
        messageLabel.font = [UIFont en_proximaTitle];
        [messageLabel sizeToFit];
        messageLabel.backgroundColor = [UIColor clearColor];
        self.collectionView.backgroundView = messageLabel;
    }
}


#pragma mark - Base Class Method Implementation

- (void)updateLanguage:(NSString *)language {
    [super updateLanguage:language];
    self.emptyMessage = nil;
    self.fetchedResultsController = nil;
    [self.collectionView reloadData];
    [self p_setUpFetchedResultsController];
    [self en_pullToRefreshFromServer];
}
-(void)getLanguages{
    [[ENPlaylistManager sharedInstance] getUserSelectedLanguagesWithCompletionBlock:^(id responseData) {
        if ([responseData isKindOfClass:[NSDictionary class]]) {
            self->_languageDict = [self p_parseUserLanguagesData:responseData ForLanguageMaster:[ENLanguageMaster movieLanguageMasterObject]];
            NSMutableArray *languageMoviesArray = [self->_languageDict valueForKey:@"code"];
            if([languageMoviesArray count] > 0){
                self->_languages = [languageMoviesArray componentsJoinedByString:@","];
            }
            if([[[ENAppSetting sharedInstance] selectedMovieLanguageServerKey] isEqualToString: @""] || [[[ENAppSetting sharedInstance] selectedMovieLanguageServerKey]  isEqualToString:@"all"]){
            }else{
                self->_languages = [[ENAppSetting sharedInstance] selectedMovieLanguageServerKey];
            }
            
            self.selectedLanguage = self->_languages;
            [self p__initialSetup];
            
        }
    } withErrorBlock:^(id errorData) {
        
    }];
}
- (void)en_pullToRefreshFromServer {
    if (self.collectionView.window && self.collectionView.superview) {
        [self.refreshControl beginRefreshing];
    }

    [super languageFilterViewSetup];
    self.collectionView.showsInfiniteScrolling = YES;

    __weak typeof(self) weakSelf = self;
    [self.movieMaster fetchGenreMoviesfromIndex:0
                                    languageKey:self.selectedLanguage
                                       masterId:self.movieMaster.masterId
                                     errorBlock:^(id errorData) {
                                         dispatch_async(dispatch_get_main_queue(), ^{
                                             [weakSelf.refreshControl endRefreshing];
                                             [weakSelf p_alertMessageSetup];
                                             [weakSelf.collectionView.infiniteScrollingView stopAnimating];
                                         });
                                     }];


}

- (void)en_lazyLoadFromServer {
    NSUInteger dataCount = [self.fetchedResultsController.fetchedObjects count];
    if (dataCount == 0) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.collectionView.infiniteScrollingView stopAnimating];
        });
        return;
    }

    __weak typeof(self) weakSelf = self;
    [self.movieMaster fetchGenreMoviesfromIndex:(int)dataCount
                                    languageKey:self.selectedLanguage
                                       masterId:self.movieMaster.masterId
                                     errorBlock:^(id errorData) {
                                         dispatch_async(dispatch_get_main_queue(), ^{
                                             [weakSelf.collectionView.infiniteScrollingView stopAnimating];
                                         });
                                     }];
}


#pragma mark - Action Methods

- (IBAction)showLanguages:(UIBarButtonItem *)sender {
    [ENUtilityVC showLanguageListModallyWithMediaType:ENLanguageMediaTypeMovie FromViewController:self];
}

#pragma mark - Language List Delegate Method

- (void)en_selectedMovieLanguage:(NSString *)languageObject {
    [self updateLanguage:languageObject];
}

- (void)p_addObservers {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(languageDidChange:) name:kMovieLanguageDidChangeNotification object:nil];
}

- (void)p_removeObservers {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kMovieLanguageDidChangeNotification object:nil];
}

- (void)languageDidChange:(NSNotification *)notification {
    [self getLanguages];
}
@end
