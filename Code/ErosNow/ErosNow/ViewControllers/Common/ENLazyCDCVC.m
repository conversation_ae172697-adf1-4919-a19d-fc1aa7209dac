//
//  ENLazyCDCVC.m
//  ErosNow
//
//  Created by <PERSON><PERSON> on 15/03/15.
//  Copyright (c) 2015 ErosNow. All rights reserved.
//

#import "ENLazyCDCVC.h"
#import "UIColor+WhiteSkin.h"

@interface ENLazyCDCVC ()

@end

@implementation ENLazyCDCVC

static NSString * const reuseIdentifier = @"Cell";

#pragma mark - View Controller Life Cycle Methods

- (instancetype)initWithCoder:(NSCoder *)aDecoder {
    if (self = [super initWithCoder:aDecoder]) {
        _toRefresh = NO;
    }

    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    if (self.viewTitle) {
        self.title = self.viewTitle;
    }
    // Do any additional setup after loading the view.
    [self p_addLazyLoading];
    [self p_addUserDidChangeNotificationObservers];
    if (self.toRefresh) {
        [self en_pullToRefreshFromServer];
    }
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (void)dealloc {
    [self p_removeNotificationObservers];
}


- (void)en_pullToRefreshFromServer {
    // empty implementation, subclass needs to implement it.
}

- (void)en_lazyLoadFromServer {
    // empty implementation, subclass needs to implement it.
    
}

- (void)stopDataRefresh {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.refreshControl endRefreshing];
    });
}

#pragma mark - Private Methods

- (void)p_addLazyLoading {
    __weak typeof(self) weakSelf = self;
    // refresh new data when pull the collection view
    if (!_refreshControl) {
        _refreshControl = [[UIRefreshControl alloc] init];
        [_refreshControl addTarget:self action:@selector(en_pullToRefreshFromServer) forControlEvents:UIControlEventValueChanged];
        _refreshControl.tintColor = [UIColor en_whiteColor];
        [self.collectionView addSubview:_refreshControl];
        self.collectionView.alwaysBounceVertical = YES;
    }

    // load more content when scroll to the bottom most
    [self.collectionView addInfiniteScrollingWithActionHandler:^{
        [weakSelf en_lazyLoadFromServer];
    }];

}

#pragma mark - Private Notification Methods

- (void)p_addUserDidChangeNotificationObservers {
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(p_reloadCollectionData) name:kUserDidChangeNotification object:nil];
}

- (void)p_removeNotificationObservers {
    [[NSNotificationCenter defaultCenter] removeObserver:self name:kUserDidChangeNotification object:nil];
}

#pragma mark - Private Reload Collection DataSource Method

- (void)p_reloadCollectionData {
    [self.collectionView reloadData];
}


#pragma mark <UICollectionViewDataSource>

#pragma mark <UICollectionViewDelegate>

/*
// Uncomment this method to specify if the specified item should be highlighted during tracking
- (BOOL)collectionView:(UICollectionView *)collectionView shouldHighlightItemAtIndexPath:(NSIndexPath *)indexPath {
	return YES;
}
*/

/*
// Uncomment this method to specify if the specified item should be selected
- (BOOL)collectionView:(UICollectionView *)collectionView shouldSelectItemAtIndexPath:(NSIndexPath *)indexPath {
    return YES;
}
*/

/*
// Uncomment these methods to specify if an action menu should be displayed for the specified item, and react to actions performed on the item
- (BOOL)collectionView:(UICollectionView *)collectionView shouldShowMenuForItemAtIndexPath:(NSIndexPath *)indexPath {
	return NO;
}

- (BOOL)collectionView:(UICollectionView *)collectionView canPerformAction:(SEL)action forItemAtIndexPath:(NSIndexPath *)indexPath withSender:(id)sender {
	return NO;
}

- (void)collectionView:(UICollectionView *)collectionView performAction:(SEL)action forItemAtIndexPath:(NSIndexPath *)indexPath withSender:(id)sender {
	
}
*/

#pragma mark SCroll View delegate

//- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView{
//    [[ENUtilityVC en_rootViewController] en_toggleTabBarWithAnimation:YES];
//}
//
//- (void)scrollViewWillBeginDragging:(UIScrollView *)scrollView
//{
//    [[ENUtilityVC en_rootViewController] en_toggleTabBarWithAnimation:NO];
//}


@end
