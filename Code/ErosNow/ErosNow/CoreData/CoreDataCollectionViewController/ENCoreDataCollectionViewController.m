//
//  ENCoreDataCollectionViewController.m
//  ErosNow
//
//  Created by <PERSON><PERSON> on 29/12/14.
//  Copyright (c) 2014 ErosNow. All rights reserved.
//

#import "ENCoreDataCollectionViewController.h"
#import "ENCollectionViewHelper.h"

@interface ENCoreDataCollectionViewController ()<UICollectionViewDelegateFlowLayout> {
    
}

@property (nonatomic, strong) ENCollectionViewHelper *collectionViewHelper;

@end

@implementation ENCoreDataCollectionViewController

static NSString * const reuseIdentifier = @"Cell";

- (id)initWithCoder:(NSCoder *)aDecoder {
    if (self = [super initWithCoder:aDecoder]) {
        _collectionViewHelper = [[ENCollectionViewHelper alloc] initWithScreenWidth:[[UIScreen mainScreen] bounds].size.width];
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    
//    _objectChanges = [NSMutableArray array];
//    _sectionChanges = [NSMutableArray array];
}


#pragma mark - Fetching

- (void)performFetch
{
    if (self.fetchedResultsController) {
        if (self.fetchedResultsController.fetchRequest.predicate) {
            if (self.debug) NSLog(@"[%@ %@] fetching %@ with predicate: %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd), self.fetchedResultsController.fetchRequest.entityName, self.fetchedResultsController.fetchRequest.predicate);
        } else {
            if (self.debug) NSLog(@"[%@ %@] fetching all %@ (i.e., no predicate)", NSStringFromClass([self class]), NSStringFromSelector(_cmd), self.fetchedResultsController.fetchRequest.entityName);
        }
        NSError *error;
        [self.fetchedResultsController performFetch:&error];
        if (error) NSLog(@"[%@ %@] %@ (%@)", NSStringFromClass([self class]), NSStringFromSelector(_cmd), [error localizedDescription], [error localizedFailureReason]);
    } else {
        if (self.debug) NSLog(@"[%@ %@] no NSFetchedResultsController (yet?)", NSStringFromClass([self class]), NSStringFromSelector(_cmd));
    }
    
    [self.collectionView reloadData];
}

- (void)setFetchedResultsController:(NSFetchedResultsController *)newfrc
{
    NSFetchedResultsController *oldfrc = _fetchedResultsController;
    if (newfrc != oldfrc) {
        _fetchedResultsController = newfrc;
        newfrc.delegate = self;
        if ((!self.title || [self.title isEqualToString:oldfrc.fetchRequest.entity.name]) && (!self.navigationController || !self.navigationItem.title)) {
            self.title = newfrc.fetchRequest.entity.name;
        }
        if (newfrc) {
            if (self.debug) NSLog(@"[%@ %@] %@", NSStringFromClass([self class]), NSStringFromSelector(_cmd), oldfrc ? @"updated" : @"set");
            [self performFetch];
        } else {
            if (self.debug) NSLog(@"[%@ %@] reset to nil", NSStringFromClass([self class]), NSStringFromSelector(_cmd));
            
            [self.collectionView reloadData];
        }
    }
    
}


#pragma mark <UICollectionViewDataSource>

- (NSInteger)numberOfSectionsInCollectionView:(UICollectionView *)collectionView {
    return [[self.fetchedResultsController sections] count];
}


- (NSInteger)collectionView:(UICollectionView *)collectionView numberOfItemsInSection:(NSInteger)section {
    id <NSFetchedResultsSectionInfo> sectionInfo = [self.fetchedResultsController sections][section];
    return [sectionInfo numberOfObjects];
}

#pragma mark - UICollection View Flow Delegate Methods

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout *)collectionViewLayout sizeForItemAtIndexPath:(NSIndexPath *)indexPath {
    ENCollectionViewCellType cellType = [self en_cellTypeForIndexPath:indexPath];
    return [self.collectionViewHelper sizeForCellType:cellType];
}


- (UIEdgeInsets)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout insetForSectionAtIndex:(NSInteger)section {
    ENCollectionViewSectionType sectionType = [self en_sectionTypeForSectionIndex:section];
    return [self.collectionViewHelper edgeInsectForSectionType:sectionType];
}

- (CGSize)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout referenceSizeForHeaderInSection:(NSInteger)section {
    ENCollectionViewSectionType sectionType = [self en_sectionTypeForSectionIndex:section];
    return [self.collectionViewHelper sizeForSectionHeaderForSectionType:sectionType];
}

- (CGFloat)collectionView:(UICollectionView *)collectionView layout:(UICollectionViewLayout*)collectionViewLayout minimumInteritemSpacingForSectionAtIndex:(NSInteger)section {
    return 0.0f;
}


#pragma mark - NSFetchedResult Controller Delegate

- (void)controller:(NSFetchedResultsController *)controller didChangeSection:(id <NSFetchedResultsSectionInfo>)sectionInfo
           atIndex:(NSUInteger)sectionIndex forChangeType:(NSFetchedResultsChangeType)type
{
    NSMutableDictionary *change = [NSMutableDictionary new];

    switch(type) {
        case NSFetchedResultsChangeInsert:
            change[@(type)] = @(sectionIndex);
            break;
        case NSFetchedResultsChangeDelete:
            change[@(type)] = @(sectionIndex);
            break;
        case NSFetchedResultsChangeMove:
            break;
        case NSFetchedResultsChangeUpdate:
            break;
    }
    [_sectionChanges addObject:change];
}

- (void)controller:(NSFetchedResultsController *)controller didChangeObject:(id)anObject
       atIndexPath:(NSIndexPath *)indexPath forChangeType:(NSFetchedResultsChangeType)type
      newIndexPath:(NSIndexPath *)newIndexPath
{
    NSMutableDictionary *change = [NSMutableDictionary new];
    switch(type)
    {
        case NSFetchedResultsChangeInsert:
            change[@(type)] = newIndexPath;
            break;
        case NSFetchedResultsChangeDelete:
            change[@(type)] = indexPath;
            break;
        case NSFetchedResultsChangeUpdate:
            change[@(type)] = indexPath;
            break;
        case NSFetchedResultsChangeMove:
            change[@(type)] = @[indexPath, newIndexPath];
            break;
    }
    [_objectChanges addObject:change];
}

- (void)controllerWillChangeContent:(NSFetchedResultsController *)controller {
    _objectChanges = [[NSMutableArray alloc] init];
    _sectionChanges = [[NSMutableArray alloc] init];
}

//- (void)controllerDidChangeContent:(NSFetchedResultsController *)controller {
//    [self.collectionView performBatchUpdates:^{
//        for (NSDictionary *change in _sectionChanges) {
//            [change enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
//                NSFetchedResultsChangeType type = [key unsignedIntegerValue];
//                switch(type) {
//                    case NSFetchedResultsChangeInsert:
//                        [self.collectionView insertSections:[NSIndexSet indexSetWithIndex:[obj unsignedIntegerValue]]];
//                        break;
//                    case NSFetchedResultsChangeDelete:
//                        [self.collectionView deleteSections:[NSIndexSet indexSetWithIndex:[obj unsignedIntegerValue]]];
//                        break;
//                    case NSFetchedResultsChangeMove:
//                        break;
//                    case NSFetchedResultsChangeUpdate:
//                        break;
//                }
//            }];
//        }
//        for (NSDictionary *change in _objectChanges) {
//            [change enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
//                NSFetchedResultsChangeType type = [key unsignedIntegerValue];
//                switch(type) {
//                    case NSFetchedResultsChangeInsert:
//                        [self.collectionView insertItemsAtIndexPaths:@[obj]];
//                        break;
//                    case NSFetchedResultsChangeDelete:
//                        [self.collectionView deleteItemsAtIndexPaths:@[obj]];
//                        break;
//                    case NSFetchedResultsChangeUpdate:
//                        [self.collectionView reloadItemsAtIndexPaths:@[obj]];
//                        break;
//                    case NSFetchedResultsChangeMove:
//                        [self.collectionView moveItemAtIndexPath:obj[0] toIndexPath:obj[1]];
//                        break;
//                }
//            }];
//        }
//    } completion:^(BOOL finished) {
//        self.sectionChanges = nil;
//        self.objectChanges = nil;
//    }];
//}

- (void)controllerDidChangeContent:(NSFetchedResultsController *)controller
{
    if ([_sectionChanges count] > 0)
    {
        __weak typeof(self) weakSelf = self;
        [weakSelf.collectionView performBatchUpdates:^{
            __strong typeof(self) strongSelf = weakSelf;
            for (NSDictionary *change in strongSelf.sectionChanges)
            {
                [change enumerateKeysAndObjectsUsingBlock:^(NSNumber *key, id obj, BOOL *stop) {

                        NSFetchedResultsChangeType type = [key unsignedIntegerValue];
                        switch (type)
                        {
                            case NSFetchedResultsChangeInsert:
                                [strongSelf.collectionView insertSections:[NSIndexSet indexSetWithIndex:[obj unsignedIntegerValue]]];
                                break;
                            case NSFetchedResultsChangeDelete:
                                [strongSelf.collectionView deleteSections:[NSIndexSet indexSetWithIndex:[obj unsignedIntegerValue]]];
                                break;
                            case NSFetchedResultsChangeUpdate:
                                [strongSelf.collectionView reloadSections:[NSIndexSet indexSetWithIndex:[obj unsignedIntegerValue]]];
                                break;
                            default:
                                break;
                        }
                }];
            }
        } completion:nil];
    }

    if ([_objectChanges count] > 0 && [_sectionChanges count] == 0)
    {

        if ([self shouldReloadCollectionViewToPreventKnownIssue] || self.collectionView.window == nil) {
            // This is to prevent a bug in UICollectionView from occurring.
            // The bug presents itself when inserting the first object or deleting the last object in a collection view.
            // http://stackoverflow.com/questions/12611292/uicollectionview-assertion-failure
            // This code should be removed once the bug has been fixed, it is tracked in OpenRadar
            // http://openradar.appspot.com/12954582

            [self.collectionView reloadData];

        } else {
            __weak typeof(self) weakSelf = self;
            [self.collectionView performBatchUpdates:^{
                __strong typeof(self) strongSelf = weakSelf;
                for (NSDictionary *change in strongSelf.objectChanges)
                {
                    [change enumerateKeysAndObjectsUsingBlock:^(NSNumber *key, id obj, BOOL *stop) {

                            NSFetchedResultsChangeType type = [key unsignedIntegerValue];
                            switch (type)
                            {
                                case NSFetchedResultsChangeInsert:
                                    [strongSelf.collectionView insertItemsAtIndexPaths:@[obj]];
                                    break;
                                case NSFetchedResultsChangeDelete:
                                    [strongSelf.collectionView deleteItemsAtIndexPaths:@[obj]];
                                    break;
                                case NSFetchedResultsChangeUpdate:
                                    [strongSelf.collectionView reloadItemsAtIndexPaths:@[obj]];
                                    break;
                                case NSFetchedResultsChangeMove:
                                    [strongSelf.collectionView moveItemAtIndexPath:obj[0] toIndexPath:obj[1]];
                                    break;
                            }
                    }];
                }
            } completion:nil];
        }
    }

    [_sectionChanges removeAllObjects];
    [_objectChanges removeAllObjects];
}

- (BOOL)shouldReloadCollectionViewToPreventKnownIssue {
    __block BOOL shouldReload = NO;

    if (self.collectionView.window == nil || !self.collectionView.superview) {
        return YES;
    }

    if ([self.objectChanges count] > 1) {
        shouldReload = YES;
        return shouldReload;
    }

    if ([self.sectionChanges count] > 0) {
        shouldReload = YES;
        return shouldReload;
    }

    for (NSDictionary *change in self.objectChanges) {
        [change enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
            NSFetchedResultsChangeType type = [key unsignedIntegerValue];

            switch (type) {
                case NSFetchedResultsChangeInsert: {
                    NSIndexPath *indexPath = obj;
                    if (indexPath.section >= [self.collectionView numberOfSections]) {
                        shouldReload = YES;
                        *stop = YES;
                        break;
                    }
                    NSInteger currentItemCount = [self.collectionView numberOfItemsInSection:indexPath.section];
                    if (currentItemCount == 0 || indexPath.row == 0) {
                        shouldReload = YES;
                        *stop = YES;
                    }
                    break;
                }
                case NSFetchedResultsChangeDelete: {
                    NSIndexPath *indexPath = obj;
                    if (indexPath.section >= [self.collectionView numberOfSections]) {
                        shouldReload = YES;
                        *stop = YES;
                        break;
                    }
                    NSInteger currentItemCount = [self.collectionView numberOfItemsInSection:indexPath.section];
                    if (currentItemCount <= 1) {
                        shouldReload = YES;
                        *stop = YES;
                    }
                    break;
                }
                case NSFetchedResultsChangeUpdate: {
                    NSIndexPath *indexPath = obj;
                    if (indexPath.section >= [self.collectionView numberOfSections]) {
                        shouldReload = YES;
                        *stop = YES;
                        break;
                    }
                    NSInteger currentItemCount = [self.collectionView numberOfItemsInSection:indexPath.section];
                    if (indexPath.row >= currentItemCount) {
                        shouldReload = YES;
                        *stop = YES;
                    }
                    break;
                }
                case NSFetchedResultsChangeMove: {
                    shouldReload = YES;
                    *stop = YES;
                    break;
                }
            }
        }];

        if (shouldReload) {
            break;
        }
    }

    return shouldReload;
}

#pragma mark - Public Api Default Implementation

- (ENCollectionViewCellType)en_cellTypeForIndexPath:(NSIndexPath *)indexPath {
    return ENCollectionViewCellTypeNone;
}

- (ENCollectionViewSectionType)en_sectionTypeForSectionIndex:(NSInteger )sectionIndex {
    return ENCollectionViewSectionTypeNone;
}

@end
