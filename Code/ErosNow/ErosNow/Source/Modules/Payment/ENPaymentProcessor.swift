//
//  ENPaymentProcessor.swift
//  ErosNow
//
//  Created by <PERSON><PERSON><PERSON> on 04/09/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import Foundation
import RxSwift

typealias PaymentTypeId = String

protocol ENPaymentProcessor {
    
    var networking: NetworkService { get }
    
    func createPendingPurchase(for productId: String, country: String) -> Observable<PendingPurchase>
    func createTransaction(for subscriptionId: String, country: String, paymentTypeId: String) -> Observable<[SubscriptionState]>
    func createSubsccription(for subscriptionId: String, itunesReceipt: String, skProduct: SKProduct, country: String, paymentTypeId: String) -> Observable<[SubscriptionState]>
    func restore(iTunesReceipt: String) -> Observable<SubscriptionState>
    func fetchAllPaymentMethods() -> Observable<PaymentMethod>
    func prepareForTransaction(for productId: String, country: String) -> Observable<(PendingPurchase, PaymentTypeId)>
}

extension ENPaymentProcessor {
    
    func createPendingPurchase(for productId: String, country: String) -> Observable<PendingPurchase> {
        
        return networking
            .rxRequest(SubscriptionAPI.createPending(country, productId: productId), objectType: PendingPurchase.self)
            .retry(1)
    }
    
    func createTransaction(for subscriptionId: String, country: String, paymentTypeId: String) -> Observable<[SubscriptionState]> {
        
        return networking
            .rxRequest(SubscriptionAPI.makeTransaction(country, paymentTypeId: paymentTypeId, subscriptionIds: [subscriptionId]), objectType: [SubscriptionState].self)
            .retry(1)
    }
    
    func createSubsccription(for subscriptionId: String, itunesReceipt: String, skProduct: SKProduct, country: String, paymentTypeId: String) -> Observable<[SubscriptionState]> {
        let startTime = Date()
        let endpoint = SubscriptionAPI.createSubscription(skProduct.countryCode ?? country, paymentTypeId: paymentTypeId, subscriptionIds: [subscriptionId], iTunesReceipt: itunesReceipt, amount: skProduct.price.decimalValue.description, currency: skProduct.currencyCode)

        // Log request configuration
        Logging.log("""
            EN IAP: Creating subscription for \(subscriptionId)
            Endpoint: \(endpoint.path)
            Country: \(skProduct.countryCode ?? country)
            PaymentTypeId: \(paymentTypeId)
            Amount: \(skProduct.price.decimalValue.description)
            Currency: \(skProduct.currencyCode ?? "N/A")
            Default Network Timeout: 60s (URLSession configuration)
            Expected Max Duration: ~180s (60s × 3 attempts with retries)
            """)

        return createSubscriptionWithCustomTimeout(subscriptionId: subscriptionId, itunesReceipt: itunesReceipt, skProduct: skProduct, country: country, paymentTypeId: paymentTypeId, timeout: Constants.SubscriptionTimeout.createSubscription)
            .retry(2) // Retry twice for subscription creation endpoint
            .do(onNext: { subscriptionStates in
                let duration = Date().timeIntervalSince(startTime)
                Logging.log("EN IAP: Subscription creation successful for \(subscriptionId) - Duration: \(String(format: "%.2f", duration))s")
            }, onError: { error in
                let duration = Date().timeIntervalSince(startTime)
                Logging.log("EN IAP: Subscription creation failed for \(subscriptionId) after retries - Duration: \(String(format: "%.2f", duration))s - Error: \(error)")
            })
    }

    /// Creates subscription with custom timeout at URLSession level
    private func createSubscriptionWithCustomTimeout(subscriptionId: String, itunesReceipt: String, skProduct: SKProduct, country: String, paymentTypeId: String, timeout: TimeInterval) -> Observable<[SubscriptionState]> {

        return Observable.create { observer in
            let endpoint = SubscriptionAPI.createSubscription(skProduct.countryCode ?? country, paymentTypeId: paymentTypeId, subscriptionIds: [subscriptionId], iTunesReceipt: itunesReceipt, amount: skProduct.price.decimalValue.description, currency: skProduct.currencyCode)

            do {
                // Convert EndPointType to NetworkEndPoint, then create URLRequest with extended timeout
                let networkEndPoint = NetworkOperation<MultiEndPoint>.defaultEndpointMapping(for: endpoint)
                var urlRequest = try networkEndPoint.urlRequest()
                urlRequest.timeoutInterval = timeout

                Logging.log("EN IAP: Custom timeout applied - \(timeout)s for subscription creation")

                let task = URLSession.shared.dataTask(with: urlRequest) { data, response, error in
                    if let error = error {
                        if (error as NSError).code == NSURLErrorTimedOut {
                            Logging.log("EN IAP: Subscription creation timed out after \(timeout)s for \(subscriptionId)")
                        }
                        let appError = ErrorHandler.handleError(error)
                        observer.onError(appError)
                        return
                    }

                    guard let httpResponse = response as? HTTPURLResponse,
                          let data = data else {
                        let appError = AppError.serverError(.unknownFailure(ErrorInfo(statusCode: 0, errorMessage: "Invalid response")))
                        observer.onError(appError)
                        return
                    }

                    do {
                        // Parse the response
                        let apiResponse = try JSONDecoder().decode(APIResponse<[SubscriptionState]>.self, from: data)

                        // Check if response is successful
                        if httpResponse.statusCode >= 200 && httpResponse.statusCode < 300 {
                            observer.onNext(apiResponse.data)
                            observer.onCompleted()
                        } else {
                            let errorInfo = ErrorInfo(statusCode: httpResponse.statusCode, errorMessage: apiResponse.message ?? "Subscription creation failed")
                            let appError = AppError.serverError(.unknownFailure(errorInfo))
                            observer.onError(appError)
                        }
                    } catch {
                        let appError = ErrorHandler.handleError(error)
                        observer.onError(appError)
                    }
                }

                task.resume()

                return Disposables.create {
                    task.cancel()
                }

            } catch {
                let appError = ErrorHandler.handleError(error)
                observer.onError(appError)
                return Disposables.create()
            }
        }
    }

    func restore(iTunesReceipt: String) -> Observable<SubscriptionState> {
        
        return networking
            .rxRequest(SubscriptionAPI.restore(iTunesReceipt: iTunesReceipt), objectType: SubscriptionState.self)
            .retry(1)
    }
    
    func fetchAllPaymentMethods() -> Observable<PaymentMethod> {
        return networking
            .rxRequest(SubscriptionAPI.fetchPaymentMethods, objectType: PaymentMethod.self)
            .retry(1)
    }
    
    
    func prepareForTransaction(for productId: String, country: String) -> Observable<(PendingPurchase, PaymentTypeId)> {
        return Observable
            .zip(createPendingPurchase(for: productId, country: country), fetchAllPaymentMethods())
            .retry(delay: .immediate)
            .map { (pendingPurchase: PendingPurchase, paymentMethod: PaymentMethod) -> (PendingPurchase, PaymentTypeId) in
                if let paymentTypeId = paymentMethod.iap.first(where: {$0.paymentTypeName == "iTunes"})?.paymentTypeId {
                    return (pendingPurchase, String(paymentTypeId))
                } else {
                    var meta = [AnalyticsProperty: Any]()
                    meta[.info] = "Payment Type ID could not be fetched"
                    meta[.errorDescription] = "Payment Type ID not present in the response or could not be parsed"
                    AnalyticsManager.shared.track(event: .apiErrorLog, metadata: meta)
                    throw AppError.paymentError(.paymentTypeNotAvailable(ErrorInfo(statusCode: 1310)))
                }
            }
    }
}
