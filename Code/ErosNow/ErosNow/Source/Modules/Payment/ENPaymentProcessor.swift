//
//  ENPaymentProcessor.swift
//  ErosNow
//
//  Created by <PERSON><PERSON><PERSON> on 04/09/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import Foundation
import RxSwift

typealias PaymentTypeId = String

protocol ENPaymentProcessor {
    
    var networking: NetworkService { get }
    
    func createPendingPurchase(for productId: String, country: String) -> Observable<PendingPurchase>
    func createTransaction(for subscriptionId: String, country: String, paymentTypeId: String) -> Observable<[SubscriptionState]>
    func createSubsccription(for subscriptionId: String, itunesReceipt: String, skProduct: SKProduct, country: String, paymentTypeId: String) -> Observable<[SubscriptionState]>
    func restore(iTunesReceipt: String) -> Observable<SubscriptionState>
    func fetchAllPaymentMethods() -> Observable<PaymentMethod>
    func prepareForTransaction(for productId: String, country: String) -> Observable<(PendingPurchase, PaymentTypeId)>
}

extension ENPaymentProcessor {
    
    func createPendingPurchase(for productId: String, country: String) -> Observable<PendingPurchase> {
        let startTime = Date()
        Logging.log("EN IAP: Creating pending purchase for product \(productId)")

        return networking
            .rxRequest(SubscriptionAPI.createPending(country, productId: productId), objectType: PendingPurchase.self)
            .timeoutForPayment(Constants.Payment.operationTimeout)
            .do(onNext: { _ in
                let duration = Date().timeIntervalSince(startTime)
                Logging.log("EN IAP: Pending purchase created - Duration: \(duration)s")
            }, onError: { error in
                let duration = Date().timeIntervalSince(startTime)
                Logging.log("EN IAP: Pending purchase failed - Duration: \(duration)s - Error: \(error)")
            })
            // Reduced retry count since network layer already handles retries
    }
    
    func createTransaction(for subscriptionId: String, country: String, paymentTypeId: String) -> Observable<[SubscriptionState]> {
        let startTime = Date()
        Logging.log("EN IAP: Creating transaction for subscription \(subscriptionId)")

        return networking
            .rxRequest(SubscriptionAPI.makeTransaction(country, paymentTypeId: paymentTypeId, subscriptionIds: [subscriptionId]), objectType: [SubscriptionState].self)
            .timeoutForPayment(Constants.Payment.operationTimeout)
            .do(onNext: { _ in
                let duration = Date().timeIntervalSince(startTime)
                Logging.log("EN IAP: Transaction created - Duration: \(duration)s")
            }, onError: { error in
                let duration = Date().timeIntervalSince(startTime)
                Logging.log("EN IAP: Transaction failed - Duration: \(duration)s - Error: \(error)")
            })
            // Reduced retry count since network layer already handles retries
    }
    
    func createSubsccription(for subscriptionId: String, itunesReceipt: String, skProduct: SKProduct, country: String, paymentTypeId: String) -> Observable<[SubscriptionState]> {
        let startTime = Date()
        Logging.log("EN IAP: Creating subscription for \(subscriptionId)")

        return networking
            .rxRequest(SubscriptionAPI.createSubscription(skProduct.countryCode ?? country, paymentTypeId: paymentTypeId, subscriptionIds: [subscriptionId], iTunesReceipt: itunesReceipt, amount: skProduct.price.decimalValue.description, currency: skProduct.currencyCode), objectType: [SubscriptionState].self)
            .timeoutForPayment(Constants.Payment.operationTimeout)
            .do(onNext: { _ in
                let duration = Date().timeIntervalSince(startTime)
                Logging.log("EN IAP: Subscription created - Duration: \(duration)s")
            }, onError: { error in
                let duration = Date().timeIntervalSince(startTime)
                Logging.log("EN IAP: Subscription creation failed - Duration: \(duration)s - Error: \(error)")
            })
            // Reduced retry count since network layer already handles retries
    }
    
    func restore(iTunesReceipt: String) -> Observable<SubscriptionState> {
        
        return networking
            .rxRequest(SubscriptionAPI.restore(iTunesReceipt: iTunesReceipt), objectType: SubscriptionState.self)
            .retry(1)
    }
    
    func fetchAllPaymentMethods() -> Observable<PaymentMethod> {
        return networking
            .rxRequest(SubscriptionAPI.fetchPaymentMethods, objectType: PaymentMethod.self)
            .timeoutForPayment(Constants.Payment.operationTimeout)
            // Reduced retry count since network layer already handles retries
    }
    
    
    func prepareForTransaction(for productId: String, country: String) -> Observable<(PendingPurchase, PaymentTypeId)> {
        return Observable
            .zip(createPendingPurchase(for: productId, country: country), fetchAllPaymentMethods())
            .retry(maxAttemptCount: 1, delay: .immediate)
            .map { (pendingPurchase: PendingPurchase, paymentMethod: PaymentMethod) -> (PendingPurchase, PaymentTypeId) in
                if let paymentTypeId = paymentMethod.iap.first(where: {$0.paymentTypeName == "iTunes"})?.paymentTypeId {
                    return (pendingPurchase, String(paymentTypeId))
                } else {
                    var meta = [AnalyticsProperty: Any]()
                    meta[.info] = "Payment Type ID could not be fetched"
                    meta[.errorDescription] = "Payment Type ID not present in the response or could not be parsed"
                    AnalyticsManager.shared.track(event: .apiErrorLog, metadata: meta)
                    throw AppError.paymentError(.paymentTypeNotAvailable(ErrorInfo(statusCode: 1310)))
                }
            }
    }
}
