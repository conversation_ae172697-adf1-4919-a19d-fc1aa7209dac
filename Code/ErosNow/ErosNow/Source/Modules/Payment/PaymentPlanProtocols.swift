//
//  PaymentPlanProtocols.swift
//  ErosNow
//
//  Created by <PERSON><PERSON><PERSON> on 22/07/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import Foundation
import UIKit
import RxSwift
import EasyIAP


enum PrePaymentUserRoute {
    case directlyPay
    case loginAndPay
}

enum PostPaymentUserRoute {
    case home
    case preference
}

enum PaymentMode {
    case newPayment
    case upgrade
}


//MARK: View
protocol PaymentPlanViewInput: BaseViewInput {
    
    // shows skip button to skip select payment
    func showSkipButton()
    
    /// Data has been fetched from the API and StoreKit. Now render the UI
    ///
    /// - Parameter viewModel: The viewModel to be used for rendering the UI
    func refreshUI(with viewModel: PlanViewModel)
    
    
    /// A plan has been selected by the user. Update the UI with the selected plan
    ///
    /// - Parameter plan: The latest plan selected
    func showSelectedPlanView(with plan: SelectedPlan)
    
    /// Something went wrong in fetching Plans. Show error on the screen
    func showPlanError()
    
    ///Displaying any error in the Snackbar
    func showErrorInSnackbar(with message: String)
    
    /// Something went wrong in IAP payment
    func showPaymentError()
    
    /// Something went wrong in Subscription Activation
    func showMoneyDeductedError()
    
    func getAnalyticsFunnelMeta() -> [AnalyticsProperty: Any]
}

//MARK: Presenter
protocol PaymentPlanViewOutput: AnyObject {
    
    // Called on viewDidLoad from viewController viewDidLoad
    func viewDidLoad()
    
    /// Tells the presenter to start loading assets
    func fetchPlans()
    
    /// Appends iTunes messages, etc
    func refreshTnCMessages()
    
    /// Select the plan
    ///
    /// - Parameter plan: The plan selected by the user from the UI
    func select(type: SubscriptionType, indexPath: IndexPath)
    
    /// The primary CTA of the module
    func continueAction(isRetry: Bool)
    
    /// Tells about the User's route after CTA
    var userRoute: PrePaymentUserRoute { get }
    
    ///Tells whether the plan is in selected state
    ///
    /// - Parameter plan: The operand
    /// - Returns: Bool value representing whetehr the passed plan is selected
    func isSelected(type: SubscriptionType, indexPath: IndexPath) -> Bool
    
    // User want to skip making payment on selecting plan
    func skipPaymentPlan()
    
    /// Open Privacy policy page
    func privacyPolicyTapped()
    
    /// Open Eros now T&C page
    func tNcTapped()
    
    /// Open Youtube Offer Terms and Conditions URL in Safari Browser
    func youtubeOfferUrlTapped()
    
    /// Checks if iTunes_Premium_Quarterly plan included in given payment sections
    /// - Parameter paymentSections: enum representing PaymentPlans
    /// - Returns: SubscriptionPlan if matching quarterly plan is included in the paymentSections array
    func isQuarterlyMachingPlanIncluded(inPaymentSections paymentSections: [PaymentSection]) -> SubscriptionPlan?
    
    func sectionActionTapped(type: SubscriptionType)

    /// Route user to appropriate screen after successful payment
    func routeUserPostPayment()
}

protocol PaymentPlanModuleInput: AnyObject {
    //MARK: Presenter Variables
    var paymentMode: PaymentMode! { get set }
    var view: PaymentPlanViewInput? { get set }
    var interactor: PaymentPlanInteractorInput! { get set }
    var router: PaymentPlanRouterInput! { get set }
}

//MARK: Interactor
protocol PaymentPlanInteractorInput: ENPaymentProcessor {
    
    /// First loads Subscription Plans from the API and then fetches respective product from StoreKit
    ///
    /// - Returns: RxSwift Observable PlanGroup
    func fetchPlans(entitlementGroups: [EntitlementGroup]) -> Observable<[SubscriptionType]>
    
    /// Continue with iTunes IAP Payment
    func processNewPayment(for plan: SubscriptionPlan) -> Observable<SubscriptionState>
    
    /// Upgrade Plan with iTunes IAP Payment
    //DON'T DELETE THIS METHOD
    //func processUpgradePayment(for plan: SubscriptionPlan) -> Observable<SubscriptionState>
    
    /// Retrieve Active Products from the user account after subscription is activated
    ///
    /// - Returns: RxSwift Observable Bool
    func getActiveProducts() -> Observable<Bool>
    
    func createAVODSubscription(plan: Plan, funnelMeta: [AnalyticsProperty: Any]) -> Observable<AVODSubscriptionState>
}

//MARK: Router
protocol PaymentPlanRouterInput: AnyObject {
    
    /// Routes to the Sign Up screen
    func routeToSignupScreen()
    
    /// Payment is done. Route to the Thank You page
    func routeToThankYouPage()
    
    /// Payment is done. Route to the User Preference screen to take inputs from the user
    func routeToUserPreferenceScreen()
    
    /// Payment is done. Route the user to the Homepage
    func routeToHome()
    
    /// dismiss viewcontroller if is presented in case of force login
    func dismissViewControllerIfPresented()
    
    /// Redirect user to home screen
    func navigateToHomeScreen()
    
    /// Open Privacy policy page
    func navigateToPrivacyPolicy()
    
    /// Open Eros now T&C page
    func navigateToTnC()
    
    /// Open Youtube offer page
    func navigateToYoutubeOfferPage()
    
    func showDetailsPopup(type: PopupViewType)
}

//MARK: PaymentPlanModuleBuilder
protocol PaymentPlanBuilder {
    
    /// Builds the Payment Plan module with all dependencies
    ///
    /// - Parameter paymentMode: .newPayment or .upgrade situation
    /// - Returns: returns the PaymentPlan UIViewController
    static func buildModule(paymentMode: PaymentMode, canSkipPayment: Bool, entitlementGroups: [EntitlementGroup]) -> PaymentPlanViewController
}

extension PaymentPlanBuilder {
    
    static func buildModule(paymentMode: PaymentMode) -> PaymentPlanViewController {
        return buildModule(paymentMode: paymentMode, canSkipPayment: true, entitlementGroups: [.svod, .avod]) // by default skip button will be added to skip payment
    }
    
    static func buildModule(paymentMode: PaymentMode, canSkipPayment: Bool) -> PaymentPlanViewController {
        return buildModule(paymentMode: paymentMode, canSkipPayment: canSkipPayment, entitlementGroups: [.svod, .avod])
    }
    
    static func buildModule(paymentMode: PaymentMode, entitlementGroups: [EntitlementGroup]) -> PaymentPlanViewController {
        return buildModule(paymentMode: paymentMode, canSkipPayment: true, entitlementGroups: entitlementGroups) // by default skip button will be added to skip payment
    }
}

