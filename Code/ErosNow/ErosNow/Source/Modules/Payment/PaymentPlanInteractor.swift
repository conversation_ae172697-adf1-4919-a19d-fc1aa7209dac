//
//  PaymentPlanInteractor.swift
//  ErosNow
//
//  Created by <PERSON><PERSON><PERSON> on 22/07/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import Foundation
import RxSwift
import EasyIAP

final class PaymentPlanInteractor: PaymentPlanInteractorInput {
    
    // MARK:- Properties
    
    private let disposeBag = DisposeBag()
    internal var networking: NetworkService
    private var accountService: AccountService
    private var iapService: IAPService
    private var avodService: AVODPaymentService
    
    // MARK:- Initialization
    
    init(networking: NetworkService, accountService: AccountService, iapService: IAPService, avodService: AVODPaymentService) {
        self.networking = networking
        self.accountService = accountService
        self.iapService = iapService
        self.avodService = avodService
    }
    
    // MARK:- PaymentPlanInteractorInput methods
    
    func fetchPlans(entitlementGroups: [EntitlementGroup]) -> Observable<[SubscriptionType]> {
        return networking.rxRequest(
            SubscriptionAPI.fetchPlans(entitlementGroups: entitlementGroups), objectType: PlanData.self
        )
        .flatMap { [weak self, iapService] (planResponse: PlanData) -> Observable<[SubscriptionType]> in
            guard self != nil else {
                return .empty()
            }
            guard let productIds = iapService.getIAPProductIds(from: planResponse) else {
                // no IAP plans mapped at the backend
                throw AppError.planError(.noIapPlansInBackend(ErrorInfo(statusCode: 1201)))
            }
            return iapService
                .fetchProductsFromStore(productIds)
                .catch { (error) in
                    // received EasyIAP EIError
                    throw AppError.planError(.storeFetchError(error, ErrorInfo(statusCode: 1203)))
                }
                .map { (storeResult: RetrieveResults) -> RetrieveResults in
                    guard let validProducts = storeResult.retrievedProducts, !validProducts.isEmpty else {
                        // no plans fetched from StoreKit
                        throw AppError.planError(.zeroPlans(ErrorInfo(statusCode: 1202)))
                    }
                    return storeResult
                }
                .map { (storeResult: RetrieveResults) -> [SubscriptionType] in
                    guard let subscriptionTypes = iapService.merge(storeResult, and: planResponse) else {
                        // IAP to EN Plans mapping error
                        throw AppError.planError(.planMappingError(ErrorInfo(statusCode: 1204)))
                    }
                    return subscriptionTypes
                }
        }
        .do(onNext: { (types: [SubscriptionType]) in
            PaymentLogger.log(.detectPriceMismatch(subscriptionTypes: types), context: "Plans")
            for type in types {
                switch type {
                case .svod(let group):
                    if let skStoreCountryCode = group.plans.first?.skProduct.countryCode {
                        PaymentLogger.log(
                            .detectCountryMismatch(
                                appStoreCountry: skStoreCountryCode,
                                serverCountry: ENCache.shared.countryCode,
                                context: "Plans"
                            )
                        )
                        break
                    }
                default:
                    continue
                }
            }
        })
    }
    
    func processNewPayment(for plan: SubscriptionPlan) -> Observable<SubscriptionState> {
        if let skStoreCountryCode = plan.skProduct.countryCode {
            PaymentLogger.log(.detectCountryMismatch(appStoreCountry: skStoreCountryCode, serverCountry: ENCache.shared.countryCode, context: "Payment"))
        }
        
        return self.prepareForTransaction(
            for: String(plan.enPlan.productId),
            country: ENCache.shared.countryCode
        )
        .flatMap { [weak self, iapService] (purchase: PendingPurchase, paymentTypeId: PaymentTypeId) -> Observable<([SubscriptionState], PaymentTypeId)> in
            guard let self = self else {
                return .empty()
            }
            guard let subscriptionId = purchase.subscriptionId else {
                //Server didn't return a subscription ID after creating subscription
                throw AppError.paymentError(.subscriptionIdUnavailable(ErrorInfo(statusCode: 1305)))
            }
            return self.createTransaction(for: String(subscriptionId), country: ENCache.shared.countryCode, paymentTypeId: paymentTypeId)
                .do(onNext: { (subscriptionStates: [SubscriptionState]) in
                    if let subscriptionState = subscriptionStates.first {
                        iapService.savePendingTxnInfo(
                            pendingSubscriptionId: String(subscriptionState.subscriptionId),
                            pendingProductIdentifier: plan.skProduct.productIdentifier,
                            paymentTypeId: paymentTypeId
                        )
                    }
                })
                .map{ return ($0, paymentTypeId) }
        }
        .observe(on: MainScheduler.instance)
        .flatMap { [iapService] (purchase: [SubscriptionState], paymentTypeId: PaymentTypeId) -> Observable<([SubscriptionState], PurchaseDetails, PaymentTypeId)> in
            return iapService.makePayment(for: plan.skProduct)
                .timeoutForPayment(Constants.Payment.iapTimeout)
                .catch { (error) -> Observable<PurchaseDetails> in
                    if case EIError.paymentError(.cancelled) = error {
                        // User cancelled the payment. Don't show any error popup
                        throw AppError.paymentError(.userCancelled(ErrorInfo(statusCode: 1301)))
                    }
                    
                    if case EIError.paymentError(.canNotMakePayment) = error {
                        // SKPaymentQueue.canMakePayments() is false. This must be the simulator
                        throw AppError.paymentError(.deviceNotEligibleForIAP(ErrorInfo(statusCode: 1302, errorMessage: Strings.Error.deviceNotEligibleForIAP)))
                    }
                    
                    if case EIError.paymentError(.deferred) = error {
                        // Payment was being done by a child. Now payment request has been deferred to the parent
                        throw AppError.paymentError(.deferred(ErrorInfo(statusCode: 1307, errorMessage: Strings.Payment.paymentDeferred)))
                    }
                    
                    // Received EasyIAP EIError
                    throw AppError.paymentError(.iapError(error, ErrorInfo(statusCode: 1303)))
                }
                .map { return (purchase, $0, paymentTypeId) }
        }
        .flatMap { [iapService] (result: ([SubscriptionState], PurchaseDetails, PaymentTypeId)) -> Observable<([SubscriptionState], PurchaseDetails, ItunesReceipt, PaymentTypeId)> in
            return iapService.getReceipt()
                .catch { (error) -> Observable<String> in
                    throw AppError.paymentError(.receiptUnavailable(ErrorInfo(statusCode: 1304)))
                }
                .map { return (result.0, result.1, $0, result.2) }
        }
        .observe(on: ConcurrentDispatchQueueScheduler(qos: .userInitiated))
        .flatMap { (result: ([SubscriptionState], PurchaseDetails, ItunesReceipt, PaymentTypeId)) -> Observable<([SubscriptionState], PurchaseDetails)> in
            guard let subscriptionId = result.0.first?.subscriptionId else {
                //Server didn't return a subscription ID after creating subscription
                throw AppError.paymentError(.subscriptionIdUnavailable(ErrorInfo(statusCode: 1305)))
            }
            return self.createSubsccription(for: String(subscriptionId), itunesReceipt: result.2, skProduct: plan.skProduct, country: ENCache.shared.countryCode, paymentTypeId: result.3)
                .catch { (error) -> Observable<[SubscriptionState]> in
                    // Payment completed but server activation failed
                    // Store transaction info for recovery and refresh user data
                    self.iapService.storePendingTxnInfo(subscriptionId: String(subscriptionId),
                                                       productIdentifier: plan.skProduct.productIdentifier,
                                                       paymentTypeId: result.3)

                    // Refresh user data in case subscription was actually activated
                    self.iapService.refreshUserDataAfterPayment()

                    throw AppError.paymentError(.subscriptionNotActivated(ErrorInfo(statusCode: 1306, errorMessage: "Payment completed but activation failed. We're refreshing your account status.")))
            }
            .map { return ($0, result.1) }
        }
        .flatMap { [iapService] (subscriptionStates: [SubscriptionState], purchaseDetails: PurchaseDetails) -> Observable<SubscriptionState> in
            guard let subscriptionState = subscriptionStates.first,
                  subscriptionState.subscriptionStatus == .active else {
                    // No active subscription returned from the server
                    // Activate grace period if applicable
                    throw AppError.paymentError(.subscriptionNotActivated(ErrorInfo(statusCode: 1306,  errorMessage: "Server couldn't active the subscription. Subscription ID: \(subscriptionStates.first?.subscriptionId ?? 0)")))
            }
            
            // Now the Entitlement has been activated. Finish the IAP transaction, if required
            if purchaseDetails.needsFinishTransaction {
                purchaseDetails.finishTransactionHandler()
            }
            
            // Delete the cached Txn Info
            iapService.resetPendingTxnInfo()
            
            Logging.log("EN IAP: Filter subscription state - \(subscriptionState)")
            return Observable.just(subscriptionState)
        }
    }
    
    //The below method will be used in future features where UPGRADE option will be available to the users.
    //DON'T Delete the code below.
    //It will be used as a reference in a later time.
    //Though the below code is not updates but it can be used as a reference for a later date.
//    func processUpgradePayment(for plan: SubscriptionPlan) -> Observable<SubscriptionState> {
//        if let skStoreCountryCode = plan.skProduct.countryCode {
//            PaymentLogger.log(.detectCountryMismatch(appStoreCountry: skStoreCountryCode, serverCountry: ENCache.shared.countryCode, context: "Payment"))
//        }
//
//        return iapService.makePayment(for: plan.skProduct)
//            .catchError { (error) -> Observable<PurchaseDetails> in
//                if case EIError.paymentError(.cancelled) = error {
//                    //User cancelled the payment. Don't show any error popup
//                    throw AppError.paymentError(.userCancelled(ErrorInfo(statusCode: 1301)))
//                }
//
//                if case EIError.paymentError(.canNotMakePayment) = error {
//                    //SKPaymentQueue.canMakePayments() is false. This must be the simulator
//                    throw AppError.paymentError(.deviceNotEligibleForIAP(ErrorInfo(statusCode: 1302)))
//                }
//
//                if case EIError.paymentError(.deferred) = error {
//                    //Payment was being done by a child. Now payment request has been deferred to the parent
//                    throw AppError.paymentError(.deferred(ErrorInfo(statusCode: 1307, errorMessage: Strings.Payment.paymentDeferred)))
//                }
//
//                //Received EasyIAP EIError
//                throw AppError.paymentError(.iapError(error, ErrorInfo(statusCode: 1303)))
//            }
//            .flatMap { [iapService] (result: (PurchaseDetails)) -> Observable<(PurchaseDetails, ItunesReceipt)> in
//                return iapService.getReceipt()
//                    .catchError { (error) -> Observable<String> in
//                        throw AppError.paymentError(.receiptUnavailable(ErrorInfo(statusCode: 1304)))
//                    }
//                    .map { return (result, $0) }
//            }
//            .observeOn(ConcurrentDispatchQueueScheduler(qos: .userInitiated))
//            .flatMap { (result: (PurchaseDetails, ItunesReceipt)) -> Observable<[SubscriptionState]> in
//                return self.createSubsccription(for: "150952", itunesReceipt: result.1, skProduct: plan.skProduct, country: ENCache.shared.countryCode, paymentTypeId: ENCache.shared.iTunesPaymentTypeId)
//                    .catchError { (error) -> Observable<[SubscriptionState]> in
//                        throw AppError.paymentError(.subscriptionNotActivated(ErrorInfo(statusCode: 1306, errorMessage: "Network Error")))
//                }
//            }
//            .flatMap { (subscriptionStates: [SubscriptionState]) -> Observable<SubscriptionState> in
//                guard let subscriptionState = subscriptionStates.first,
//                    subscriptionState.subscriptionStatus == .active else {
//                        // No active subscription returned from the server
//                        // Activate grace period if applicable
//                        throw AppError.paymentError(.subscriptionNotActivated(ErrorInfo(statusCode: 1306,  errorMessage: "Server couldn't active the subscription. Subscription ID: \(subscriptionStates.first?.subscriptionId ?? 0)")))
//                }
//                Logging.log("EN IAP: Filter subscription state - \(subscriptionState)")
//                return Observable.just(subscriptionState)
//            }
//    }
    
    func getActiveProducts() -> Observable<Bool> {
        return accountService.getActiveProducts()
            .observe(on: MainScheduler.instance)
            .flatMap { [accountService] (activeProduct: ActiveProduct) -> Observable<Bool> in
                accountService.saveUserAndAccountInfo(type: nil, info: nil, activeProduct: activeProduct)
                return Observable.just(true)
            }
    }
    
    func createAVODSubscription(plan: Plan, funnelMeta: [AnalyticsProperty: Any]) -> Observable<AVODSubscriptionState> {
        return avodService.getAvodSubscription(metadata: funnelMeta)
    }
    
}


//    func fetchPlans(entitlementGroups: [EntitlementGroup]) -> Observable<[SubscriptionType]> {
//
//        return networking.rxRequest(SubscriptionAPI.fetchPlans(entitlementGroups: entitlementGroups), objectType: PlanData.self)
//            .retry(1)
//            .flatMap({ [iapService] (planResponse: PlanData) -> Observable<(Set<IAPProductIdentifier>, PlanData)> in
//                guard let productIds = iapService.getIAPProductIds(from: planResponse) else {
//                    //No IAP Plans mapped at the backend
//                    throw AppError.planError(.noIapPlansInBackend(ErrorInfo(statusCode: 1201)))
//                }
//                return Observable.just((productIds, planResponse))
//            })
//            .observeOn(MainScheduler.instance)
//            .flatMap({[iapService] (result: (productIds: Set<IAPProductIdentifier>, planResponse: PlanData)) -> Observable<(RetrieveResults, PlanData)> in
//
//                return iapService.fetchProductsFromStore(result.productIds)
//                    .catchError({ (error) -> Observable<RetrieveResults> in
//
//                        //Received EasyIAP EIError
//                        throw AppError.planError(.storeFetchError(error, ErrorInfo(statusCode: 1203)))
//                    })
//                    .flatMap({ (storeResult: RetrieveResults) -> Observable<(RetrieveResults, PlanData)> in
//
//                        guard let validProducts = storeResult.retrievedProducts, validProducts.count > 0 else {
//                            //No plans fetched from StoreKit
//                            throw AppError.planError(.zeroPlans(ErrorInfo(statusCode: 1202)))
//                        }
//
//                        return Observable.just((storeResult, result.planResponse))
//                    })
//            })
//            .observeOn(ConcurrentDispatchQueueScheduler(qos: .userInitiated))
//            .flatMap({[iapService] (result: (storeResult: RetrieveResults, planResponse: PlanData)) -> Observable<[SubscriptionType]> in
//                guard let subscriptionTypes = iapService.merge(result.storeResult, and: result.planResponse) else {
//                    //IAP to EN Plans mapping error
//                    throw AppError.planError(.planMappingError(ErrorInfo(statusCode: 1204)))
//                }
//
//                return Observable.just(subscriptionTypes)
//            })
//            .do(onNext: { (types: [SubscriptionType]) in
//
//                PaymentLogger.log(.detectPriceMismatch(subscriptionTypes: types), context: "Plans")
//
//                for type in types {
//                   switch type {
//                    case .tvod(let group), .svod(let group):
//                        if let skStoreCountryCode = group.plans.first?.skProduct.countryCode {
//                            PaymentLogger.log(.detectCountryMismatch(appStoreCountry: skStoreCountryCode, serverCountry: ENCache.shared.countryCode, context: "Plans"))
//                            break
//                        }
//                    default:
//                        continue
//                    }
//                }
//            })
//    }
