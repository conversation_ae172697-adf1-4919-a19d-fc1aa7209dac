//
//  PaymentPlanPresenter.swift
//  ErosNow
//
//  Created by <PERSON><PERSON><PERSON> on 22/07/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import Foundation
import RxSwift

enum PaymentAnalytics {
    case postPayment(plan: SelectedPlan, error: String?)
    case planSelected(plan: SelectedPlan)
    case `continue`(plan: SelectedPlan)
    case payNow(plan: SelectedPlan)
}

final class PaymentPlanPresenter: PaymentPlanViewOutput, PaymentPlanModuleInput {
    
    //MARK: Properties
    weak var view: PaymentPlanViewInput?
    var router: PaymentPlanRouterInput!
    var interactor: PaymentPlanInteractorInput!
    var paymentMode: PaymentMode!
    var selectedPlan: SelectedPlan?
    let disposeBag = DisposeBag()
    var planVm : PlanViewModel?
    private var canSkipPayment: Bool = false
    private var entitlementGroups: [EntitlementGroup] = []
    
    var userRoute: PrePaymentUserRoute {
        let isLoggedIn = ENCache.shared.isLoggedIn
        let route: PrePaymentUserRoute = isLoggedIn ? .directlyPay : .loginAndPay
        Logging.log("EN IAP: User route determined - isLoggedIn: \(isLoggedIn), route: \(route)")
        return route
    }
    
    //MARK:- Initialization
    init(paymentMode: PaymentMode, canSkipPayment: Bool, entitlementGroups: [EntitlementGroup]) {
        self.paymentMode = paymentMode
        self.canSkipPayment = canSkipPayment
        self.entitlementGroups = entitlementGroups
    }
    
    func viewDidLoad() {
        if self.canSkipPayment {
            self.view?.showSkipButton()
        }
        
        //Send page view analytics event
        var meta = [AnalyticsProperty: Any]()
        meta[.categoryName] = AnalyticsCategory.purchase.rawValue
        meta[.screenName] = ENScreenName.paymentsPage.rawValue
        AnalyticsManager.shared.track(event: .subscriptionPlanViewed, metadata: meta)
    }
    
    //MARK:- PaymentPlanViewOutput methods
    
    func skipPaymentPlan() {
        router.navigateToHomeScreen()
    }
    
    func sectionActionTapped(type: SubscriptionType) {
        switch type {
        case .avod:
            self.router.showDetailsPopup(type: .avodDetails)
        case .svod:
            self.router.showDetailsPopup(type: .svodBenefits)
        default:
            break
        }
    }
    
    func fetchPlans() {
        Logging.log("EN IAP: fetchPlans called - entitlementGroups: \(entitlementGroups)")

        view?.showSpinner("Fetching plans")
        interactor
            .fetchPlans(entitlementGroups: entitlementGroups)
            .flatMap { [weak self] (types: [SubscriptionType]) -> Observable<PlanViewModel> in
                guard let self = self else {
                    return .empty()
                }

                Logging.log("EN IAP: Plans fetched successfully - \(types.count) subscription types")
                for (index, type) in types.enumerated() {
                    switch type {
                    case .svod(let group):
                        Logging.log("EN IAP: SVOD group \(index) has \(group.plans.count) plans")
                        for plan in group.plans {
                            Logging.log("EN IAP: - Plan: \(plan.enPlan.planDisplayName) (\(plan.skProduct.productIdentifier))")
                        }
                    case .tvod(let group):
                        Logging.log("EN IAP: TVOD group \(index) has \(group.plans.count) plans")
                    case .avod(let group):
                        Logging.log("EN IAP: AVOD group \(index) has \(group.plans.count) plans")
                    }
                }

                var sections = [PaymentSection]()
                for type in types {
                    sections.append(.plan(type))
                }
                var planViewModel = PlanViewModel(sections: sections)
                planViewModel = self.appendMessages(to: planViewModel)
                return Observable.just(planViewModel)
            }
            .applyDefaultSchedulers()
            .subscribe(onNext: { [weak self] (planVm: PlanViewModel) in
                guard let self = self else {
                    return
                }
                self.planVm = planVm
                self.view?.hideSpinner()

                Logging.log("EN IAP: UI refreshed with plan view model - \(planVm.sections.count) sections")

                self.view?.refreshUI(with: planVm)
            }, onError: { [weak self] (error: Error) in
                guard let self = self else { return }
                self.view?.hideSpinner()

                Logging.log("EN IAP: ❌ Failed to fetch plans - \(error)")

                self.view?.showPlanError()
                if let appError = error as? AppError {
                    PaymentLogger.log(.paymentFlow(error: appError))
                }
            })
            .disposed(by: disposeBag)
    }
    
    func select(type: SubscriptionType, indexPath: IndexPath) {
        Logging.log("EN IAP: Plan selection - type: \(type), indexPath: \(indexPath)")

        let newSelectedPlan: SelectedPlan
        switch type {
        case .avod(let group):
            newSelectedPlan = .avod(plan: group.plans[indexPath.row])
        case .tvod(let group):
            newSelectedPlan = .tvod(plan: group.plans[indexPath.row])
        case .svod(let group):
            newSelectedPlan = .svod(plan: group.plans[indexPath.row])
        }

        Logging.log("EN IAP: New selected plan: \(newSelectedPlan.planName) - \(newSelectedPlan.planPrice)")

        if let sPlan = selectedPlan {
            if sPlan != newSelectedPlan {
                Logging.log("EN IAP: Changing plan selection from \(sPlan.planName) to \(newSelectedPlan.planName)")
                save(newSelectedPlan)
            } else {
                Logging.log("EN IAP: Same plan selected, no change needed")
            }
        } else {
            Logging.log("EN IAP: First plan selection")
            save(newSelectedPlan)
        }
    }
    
    func isSelected(type: SubscriptionType, indexPath: IndexPath) -> Bool {
        guard let selectedPlan = selectedPlan else { return false }
        
        let plan: SelectedPlan
        switch type {
        case .avod(let group):
            plan = .avod(plan: group.plans[indexPath.row])
        case .tvod(let group):
            plan = .tvod(plan: group.plans[indexPath.row])
        case .svod(let group):
            plan = .svod(plan: group.plans[indexPath.row])
        }
        
        return plan == selectedPlan
    }
    
    func continueAction(isRetry: Bool) {
        Logging.log("EN IAP: continueAction called - isRetry: \(isRetry)")

        guard let _selectedPlan = selectedPlan else {
            Logging.log("EN IAP: ❌ No plan selected! selectedPlan is nil")
            view?.showErrorInSnackbar(with: "Please select a plan first")
            return
        }

        Logging.log("EN IAP: Selected plan: \(_selectedPlan.planName) - \(_selectedPlan.planPrice)")
        Logging.log("EN IAP: User route: \(userRoute)")

        switch userRoute {
            
        case .directlyPay:
            var subscriptionPlan: SubscriptionPlan?
            
            switch _selectedPlan {
            case .avod(let plan):
                createAVODSubscription(plan: plan)
                return
                
            case .tvod(let plan), .svod(let plan):
                subscriptionPlan = plan
            }
            
            guard let _subscriptionPlan = subscriptionPlan else { return }
            
            Logging.log("EN IAP: Payment Started")
            view?.showSpinner("Processing...")
            interactor
                .processNewPayment(for: _subscriptionPlan)
                .applyDefaultSchedulers()
                .subscribe(onNext: {(purchaseResult) in
                    
                    Logging.log("EN IAP: onNext - \(purchaseResult.subscriptionStatus)")
                    //Get updated payment status
                    
                }, onError: {[weak self] (error) in
                    guard let self = self else {return}
                    Logging.log("EN IAP: onError - \(error)")

                    self.view?.hideSpinner()

                    // Refresh user data even on payment failure to get latest subscription status
                    IAPService.shared.refreshUserDataAfterPayment()

                    guard let appError = error as? AppError else {
                        self.view?.showPaymentError()
                        return
                    }
                    self.handlePaymentError(appError)
                    }, onCompleted: { [weak self] in
                        guard let self = self else {return}
                        Logging.log("EN IAP: onCompleted - Proceed to getActiveProducts")

                        var metadata = self.getAnalyicsMeta(from: .postPayment(plan: _selectedPlan, error: nil))
                        metadata[.categoryName] = AnalyticsCategory.purchase.getValue()
                        AnalyticsManager.shared.track(event: .purchaseSuccess, metadata: metadata)

                        // Refresh user data after successful payment
                        IAPService.shared.refreshUserDataAfterPayment()

                        self.getActiveProducts()
                })
                .disposed(by: disposeBag)
            
            //Analytics
            if isRetry {
                firePaymentRetry()
            } else {
                var metadata = getAnalyicsMeta(from: .payNow(plan: _selectedPlan))
                metadata[.categoryName] = AnalyticsCategory.purchase.getValue()
                AnalyticsManager.shared.track(event: .paynowClicked, metadata: metadata)
            }
            
        case .loginAndPay:
            let defaults = UserDefaultsService()
            defaults.set(true, forKey: .paymentInitiated)
            router.routeToSignupScreen()
            
            //Analytics
            var metadata = getAnalyicsMeta(from: .continue(plan: _selectedPlan))
            metadata[.categoryName] = AnalyticsCategory.purchase.getValue()
            AnalyticsManager.shared.track(event: .continuePaymentClicked, metadata: metadata)
        }
    }
    
    private func createAVODSubscription(plan: Plan) {
        view?.showSpinner("Processing...")
        var meta: [AnalyticsProperty: Any] = [
            .screenName: ENScreenName.paymentsPage.rawValue,
        ]
        let funnelMeta = self.view?.getAnalyticsFunnelMeta() ?? [:]
        meta.merge(funnelMeta) { (current, _) -> Any in
            return current
        }
        interactor.createAVODSubscription(plan: plan, funnelMeta: meta)
        .applyDefaultSchedulers()
            .subscribe(onNext: { (subState) in
                Logging.log("EN AVOD: onNext - \(subState.subscriptionStatus)")
            }, onError: { [weak self] (error) in
                guard let self = self else { return }
                Logging.log("EN AVOD: onError - \(error)")
                self.view?.hideSpinner()
                
                guard let appError = error as? AppError else {
                    self.view?.showPaymentError()
                    #warning("Error to be shown for AVOD failure")
                    return
                }
                
                if appError.errorInfo.apiStatusCode == .alreadySubscribed {
                    self.view?.showErrorInSnackbar(with: appError.errorInfo.errorMessage ?? "Already subscribed")
                    return
                }
                self.handlePaymentError(appError)
            }, onCompleted: { [weak self] in
                guard let self = self else { return }
                Logging.log("EN AVOD: onCompleted")
                self.view?.hideSpinner()

                // Refresh user data after AVOD payment completion
                IAPService.shared.refreshUserDataAfterPayment()

                self.routeUserPostPayment()
            })
        .disposed(by: disposeBag)
    }
    
    func refreshTnCMessages() {
        
        guard let oldPlanVm = self.planVm else { return }
        let newPlanVm = appendMessages(to: oldPlanVm)
        planVm = newPlanVm
        self.view?.refreshUI(with: newPlanVm)
    }
    
    func privacyPolicyTapped() {
        router.navigateToPrivacyPolicy()
    }
    
    func tNcTapped() {
        router.navigateToTnC()
    }
    
    // MARK:- Private Methods
    
    private func handlePaymentError(_ error: AppError) {
        
        //Safety Log
        PaymentLogger.log(.paymentFlow(error: error))
        
        switch error {
        case .paymentError(let error):
            switch error{
            case .subscriptionNotActivated,
                 .subscriptionIdUnavailable:
                view?.showMoneyDeductedError()
            case .userCancelled(_):
                //Log events
                fireIapError(message: "User cancelled the IAP payment")
            case .deviceNotEligibleForIAP(_):
                view?.showErrorInSnackbar(with: error.localizedDescription)
            case .deferred(_):
                view?.showErrorInSnackbar(with: error.localizedDescription)
            case .iapError(let error, _):
                fireIapError(message: error.localizedDescription)
                fallthrough
            default:
                view?.showPaymentError()
            }
            
        case .serverError(_), .httpError(_):
            view?.showPaymentError()
            
        default: fatalError("Wrong Error type thrown from Payment module")
        }
        
        guard let selectedPlan = self.selectedPlan else { return }
        var metadata = getAnalyicsMeta(from: .postPayment(plan: selectedPlan, error: error.failureReason))
        metadata[.categoryName] = AnalyticsCategory.purchase.getValue()
        AnalyticsManager.shared.track(event: .purchaseFailed, metadata: metadata)
    }
    
    private func getActiveProducts() {
        interactor.getActiveProducts()
            .applyDefaultSchedulers()
            .subscribe(onNext: {(purchaseResult) in
                
            }, onError: {[weak self] (error) in
                guard let self = self else {return}
                if let appError = error as? AppError {
                    self.handlePaymentError(appError)
                }
                self.view?.hideSpinner()
                
                }, onCompleted: { [weak self] in
                    guard let self = self else {return}
                    self.view?.hideSpinner()
                    self.routeUserPostPayment()
            })
            .disposed(by: disposeBag)
    }
    
    private func save(_ plan: SelectedPlan) {
        selectedPlan = plan
        view?.showSelectedPlanView(with: plan)
        
        var metadata = getAnalyicsMeta(from: .planSelected(plan: plan))
        metadata[.categoryName] = AnalyticsCategory.purchase.getValue()
        switch plan {
        case .avod:
            let funnelMeta = self.view?.getAnalyticsFunnelMeta() ?? [:]
            metadata.merge(funnelMeta) { (_, new) in
                return new
            }
            AnalyticsManager.shared.track(event: .avodSelected, metadata: metadata)
            
        case .svod, .tvod:
            AnalyticsManager.shared.track(event: .planSelected, metadata: metadata)
        }
    }
    
    //MARK:- Analtyics Methods
    
    private func fireIapError(message: String? = nil) {
        
        var meta = [AnalyticsProperty: AnyHashable]()
        meta[.categoryName] = AnalyticsCategory.purchase.rawValue
        if let message = message {
            meta[.iapResponse] = message
        }
        AnalyticsManager.shared.track(event: .iapPopupCancel, metadata: meta)
    }
    
    private func firePaymentRetry() {
        
        var meta = [AnalyticsProperty: AnyHashable]()
        meta[.categoryName] = AnalyticsCategory.purchase.rawValue
        
        AnalyticsManager.shared.track(event: .retryPaymentClicked, metadata: meta)
    }
    
    private func getAnalyicsMeta(from intent: PaymentAnalytics) -> [AnalyticsProperty: Any] {
        
        var meta = [AnalyticsProperty: Any]()
        meta[.categoryName] = AnalyticsCategory.purchase.rawValue
        meta[.screenName] = ENScreenName.paymentsPage.rawValue
        
        switch intent {
        case .planSelected(let plan),
             .payNow(let plan):
            
            meta[.planName] = plan.planName
            meta[.planPrice] = plan.planPrice
            meta[.discountPrice] = plan.discountPrice
            meta[.currency] = plan.planCurrency
            
        case .continue(let plan):
            meta[.planName] = plan.planName
            meta[.finalPrice] = plan.finalPrice
            meta[.discountPrice] = plan.discountPrice
            meta[AnalyticsProperty.currency] = plan.planCurrency
            
        case .postPayment(let plan, let error):
            meta[.planName] = plan.planName
            meta[.planPrice] = plan.planPrice
            meta[.currency] = plan.planCurrency
            meta[.paymentType] = plan.paymentType
            meta[.discountPrice] = plan.discountPrice
            
            if let error = error {
                meta[.failureReason] = error
            }
        }
        
        return meta
    }
    
    private func routeUserPostPayment() {
        
        switch getPostPaymentUserRoute() {
            
        case .preference:
            self.router.routeToUserPreferenceScreen()
            
        case .home:
            self.router.routeToHome()
        }
    }
    
    private func getPostPaymentUserRoute() -> PostPaymentUserRoute {
        
        //For now Product has decided to redirect the user to Preference screen ALWAYS after payment
        //Treat Preference screen as Thank U page
        return .preference
    }
    
    private func appendMessages(to planVm: PlanViewModel) -> PlanViewModel {
        
        var tPlanVm = planVm
//        if self.userRoute == .directlyPay {
            
            if !tPlanVm.sections.contains(where: {
                if case .iTunesMessage = $0 {
                    return true
                }
                return false
            }) {
                tPlanVm.sections.append(.iTunesMessage(Strings.Payment.iTunesMessageTitle, message: Strings.Payment.iTunesMessageBody))
            }
//        }
        return tPlanVm
    }
    
}

//MARK: iTunes_Premium_Quarterly Banner

extension PaymentPlanPresenter {
    
    private func isQuarterlyMachingPlanIncluded(inPaymentPlans paymentPlans: [SubscriptionPlan]) -> SubscriptionPlan? {
        let iTunesPremiumQuarterlyPlanString = "iTunes_Premium_Quarterly"
        guard let matchingQuarterlyPlan = paymentPlans.first(where: { (plan: SubscriptionPlan) in
            return (plan.enPlan.iapProductId == iTunesPremiumQuarterlyPlanString && plan.skProduct.productIdentifier == iTunesPremiumQuarterlyPlanString)
        }) else {
            return nil
        }
        return matchingQuarterlyPlan
    }
    
    func isQuarterlyMachingPlanIncluded(inPaymentSections paymentSections: [PaymentSection]) -> SubscriptionPlan? {
        
//        var quarterlyYoutubeSubscriptionPlan: SubscriptionPlan? = nil
//
//        _ = paymentSections.first(where: { (section: PaymentSection) -> Bool in
//            switch section {
//            case let .plan(paymentPlans, _, _):
//                guard let subscriptionPlan = isQuarterlyMachingPlanIncluded(inPaymentPlans: paymentPlans) else {
//                    return false
//                }
//                quarterlyYoutubeSubscriptionPlan = subscriptionPlan
//                return true
//            default:
//                return false
//            }
//        })
//        return quarterlyYoutubeSubscriptionPlan
        
        return nil
    }
    
    func youtubeOfferUrlTapped() {
        router.navigateToYoutubeOfferPage()
    }
}
