//
//  URLSession+Defaults.swift
//  ErosNow
//
//  Created by <PERSON><PERSON><PERSON> on 20/06/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import Foundation

extension URLSessionConfiguration {
    
    ///Returns the Defaukt configuration for Eros Now APIs
    static var erosnowConfiguration: URLSessionConfiguration {
        let configuration = URLSessionConfiguration.default
        
        var headers: [String: String] = [:]
        headers[APIContstants.xPlatform]   = deviceOsPlatform.uppercased()
        headers[APIContstants.xAppVersion] = Bundle.main.releaseVersionNumber
        headers[APIContstants.xUserAgent]  = "\(Bundle.main.displayName ?? "Eros Now")/\(Bundle.main.releaseVersionNumber ?? "") (\(UIDevice.current.model) \(deviceOsNameVersion.uppercased()))"
        headers[APIContstants.xApiClient]  = ENEnvironmentHelper.environment.jwtApiClient
        headers[APIContstants.xServiceToken] = ENEnvironmentHelper.environment.jwtServiceToken
        headers[APIContstants.xDeviceId]   = UIDevice.current.deviceIdentifer
        
        let validatorSalt = "EN-\(deviceOsPlatform.uppercased())-\(Bundle.main.releaseVersionNumber ?? "")-\(UIDevice.current.deviceIdentifer)-Locx1"
        headers[APIContstants.xValidator] = validatorSalt.md5Hash
        
        if #available(iOS 11.0, tvOS 11.0, *) {
            headers["Accept-Encoding"] = "br, gzip, deflate"
        } else {
            headers["Accept-Encoding"] = "gzip, deflate"
        }
        
        configuration.httpAdditionalHeaders = headers
        
        if #available(iOS 11.0, tvOS 11.0, *) {
            configuration.waitsForConnectivity = true
        }
        // Reduced timeouts for better payment flow performance
        // Request timeout: time to establish connection and start receiving data
        configuration.timeoutIntervalForRequest = 30
        // Resource timeout: total time for entire request including retries
        configuration.timeoutIntervalForResource = 45
        return configuration
    }
    
    static var mzaaloConfiguration: URLSessionConfiguration {
        let configuration = URLSessionConfiguration.default
        
        var headers: [String: String] = [:]
        headers["Ocp-Apim-Subscription-Key"]   = "1d0caac2702049b89a305929fdf4cbae"
        
        configuration.httpAdditionalHeaders = headers
        
        if #available(iOS 11.0, tvOS 11.0, *) {
            configuration.waitsForConnectivity = true
        }
        // Reduced timeouts for better payment flow performance
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 45
        return configuration
    }
}

var deviceOsPlatform: String = {
    #if os(iOS)
    return "iOS"
    #elseif os(tvOS)
    return "tvOS"
    #else
    return "iOS"
    #endif
}()

var deviceOsNameVersion: String = {
    let version = ProcessInfo.processInfo.operatingSystemVersion
    let osVersionString = "\(version.majorVersion).\(version.minorVersion).\(version.patchVersion)"
    return "\(deviceOsPlatform) \(osVersionString)"
}()

//API and Header's constansts
public enum APIContstants {
    //Headers
    static let xApiClient    = "x-api-client"
    static let xApiToken     = "x-api-token"
    static let xServiceToken = "x-service-token"
    static let xUserAgent    = "User-Agent"
    static let xAppVersion   = "x-app-version"
    static let xPlatform     = "x-platform"
    static let xCountry      = "x-country"
    static let xDeviceId     = "x-device-id"
    static let xValidator    = "x-validator"
}
