//
//  NetworkAPIClient.swift
//  ErosNow
//
//  Created by <PERSON><PERSON> on 20/05/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import Foundation
import Networking
import RxSwift

//MARK: NetworkService
protocol NetworkService: AnyObject {
    
    @discardableResult func request<T: Decodable>(_ endPoint: EndPointType, objectType: T.Type, completion: @escaping (Swift.Result<T, AppError>) -> Void) -> CancellableRequest
    
    func rxRequest<T: Decodable>(_ endPoint: EndPointType, objectType: T.Type) -> Observable<T>
    func rxRequest<T: Decodable>(_ endPoint: EndPointType) -> Observable<T>
    
    func cancelAllRequests()
}

//MARK: Networking
final class Networking: NetworkService {
    
    static let shared = Networking(
        network: NetworkOperation<MultiEndPoint>(
            configuration: URLSessionConfiguration.erosnowConfiguration,
            interceptor: Interceptor(
                behaviours: [MandatoryParamsBehaviour(), AuthRequestBehaviour(), LoggingBehaviour()],
                retriers: []
            )
        )
    )
    
    private let network: NetworkOperation<MultiEndPoint>
    
    private init(network: NetworkOperation<MultiEndPoint>) {
        self.network = network
    }
    
    // MARK: dataRequest
    @discardableResult
    func request<T: Decodable>(_ endPoint: EndPointType, objectType: T.Type, completion: @escaping (Swift.Result<T, AppError>) -> Void) -> CancellableRequest {
        return network.request(.target(endPoint), completion: {(result: Swift.Result<Response, NetworkError>) in
            switch result {
            case .success(let response):
                do {
                    let decodedObject = try response.decode(APIResponse<T>.self)
                    completion(.success(decodedObject.data))
                } catch {
                    let appError: AppError = ErrorHandler.handleError(error)
                    completion(.failure(appError))
                }
            case .failure(let error):
                let appError: AppError = ErrorHandler.handleError(error)
                var meta = [AnalyticsProperty: Any]()
                meta[.info] = endPoint.path
                meta[.errorDescription] = appError.description
                AnalyticsManager.shared.track(event: .apiErrorLog, metadata: meta)
                completion(.failure(appError))
            }
        })
        //Retry mechanism not available for this implementation above
    }
    

    // MARK: RxRequest
    func rxRequest<T: Decodable>(_ endPoint: EndPointType, objectType: T.Type) -> Observable<T> {
        return network.rx
            .request(.target(endPoint))
            .map { (response: Response) in
                // added if statement to return decodedObject as is
                if endPoint.path.contains("closeaccount") {
                    let decodedObject = try response.decode(DeleteResponse.self)
                    print("decoded object: \(decodedObject)")
                    return decodedObject as! T
                } else if endPoint.path.contains("basicsubscription") {
                    let decodedObject = try response.decode(SwitchToBasicResponse.self)
                    print("decoded object: \(decodedObject)")
                    return decodedObject as! T
                } else if endPoint.path.contains("recaptcha/verify") {
                    let decodedObject = try response.decode(RecaptchaVerifyResponse.self)
                    print("[debug] Recaptcha decoded object: \(decodedObject)")
                    return decodedObject as! T
                }
                let decodedObject = try response.decode(APIResponse<T>.self)
                return decodedObject.data
            }
            .catch { (error: Error) in
                let appError: AppError = ErrorHandler.handleError(error)
                var meta = [AnalyticsProperty: Any]()
                meta[.info] = endPoint.path
                meta[.errorDescription] = appError.description
                AnalyticsManager.shared.track(event: .apiErrorLog, metadata: meta)
                return Observable.error(appError)
            }
            .retry(maxAttemptCount: Constants.retryCount,
                   delay: .immediate,
                   shouldRetry: { (error: Error) -> Bool in
                    //REFRESH TOKEN FLOW C
                    //TO DO: Refresh Token with policy = forceRefresh
                if case AppError.serverError(.sessionExpired) = error {
                    return true
                }
                return false
            })
    }
    
    func rxRequest<T: Decodable>(_ endPoint: EndPointType) -> Observable<T> {
        return network.rx
            .request(.target(endPoint))
            .map { (response: Response) in
                return try response.decode(T.self)
            }
            .catch { (error: Error) in
                let appError: AppError = ErrorHandler.handleError(error)
                var meta = [AnalyticsProperty: Any]()
                meta[.info] = endPoint.path
                meta[.errorDescription] = appError.description
                AnalyticsManager.shared.track(event: .apiErrorLog, metadata: meta)
                return Observable.error(appError)
            }
            .retry(maxAttemptCount: Constants.retryCount,
                   delay: .immediate,
                   shouldRetry: { (error: Error) -> Bool in
                    // REFRESH TOKEN FLOW C
                    //TO DO: Refresh Token with policy = forceRefresh
                if case AppError.serverError(.sessionExpired) = error {
                    return true
                }
                return false
            })
    }
    
    func cancelAllRequests() {
        network.cancelAllRequests()
    }
    
    // MARK:- Handle 401 Network Error
    //LEGACY WAY TO DETECT SESSION EXPIRY
    //NEW WAY IMPLEMENT WAYS TO REFRESH TOKEN IF EXPIRED
    //    public func handleNetwork(_ error: AppError, with url: String) {
    //        if case AppError.serverError(.sessionExpired(let errorInfo)) = error {
    //            let errorStr = "HTTP_CODE: \(errorInfo.statusCode) | EN_ERR_CODE: \(errorInfo.apiStatusCode?.rawValue ?? "0") | API: \(url)"
    //            DispatchQueue.main.async {
    //                NotificationCenter.default.post(name: .SessionExpired, object: nil, userInfo: [SessionLifecycle.Keys.error: errorStr])
    //            }
    //        }
    //    }
}

extension Networking {
    
    static let mzaaloService = Networking(
        network: NetworkOperation<MultiEndPoint>(
            configuration: URLSessionConfiguration.mzaaloConfiguration,
            interceptor: Interceptor(
                behaviours: [LoggingBehaviour()],
                retriers: []
            )
        )
    )
}
