//
//  IAPService.swift
//  ErosNow
//
//  Created by <PERSON><PERSON><PERSON> on 17/09/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import Foundation
import EasyIAP
import RxSwift
import Networking

public typealias IAPProductIdentifier = String

final class IAPService: NSObject {
    
    //Sandbox Deferred Testing
    private let WILL_TEST_SANDBOX_ASK_TO_BUY = false
    
    private let disposeBag = DisposeBag()
    internal var networking: NetworkService
    private var userDefaultService: UserDefaultsService
    private var accountService: AccountService
    private let restoreQueue = DispatchQueue(label: "com.erosnow.serialQueue.iapRestore")
    private let restoreSemaphor = DispatchSemaphore(value: 0)
    
    //If disabled, txns will get finished as they appear on the queue
    private var isAutoRestoreMechanismDisabled = false
    
    static let shared = IAPService(networking: Networking.shared, userDefaultService: UserDefaultsService())
    
    init(networking: NetworkService = Networking.shared, userDefaultService: UserDefaultsService) {
        self.networking = networking
        self.accountService = AccountService(networking: networking, userDefaultService: userDefaultService, avodService: MzaaloService(networking: Networking.mzaaloService))
        self.userDefaultService = userDefaultService
    }
    
    @objc func setup() {
        EasyIAP.default
            .addStorePendingPurchase(completion: { (source: PurchaseTask.Source) -> StoreIntentResponse in
                
                PaymentLogger.log(.appStorePurchase(product: source.skProduct))
                //TO BE IMPLEMENTED
                return .defer
                
            })
            .registerPurchasedHandler(handler: {[unowned self] (result: TransactionResult) in
                
                //Something was purchased in the background
                
                switch result {
                    
                case .purchased(let purchaseDetails):
                    
                    //Note: This retry mechanism logically happens only once in a lifetime
                    
                    if let pendingTxnInfo = self.getPendingTxnInfo(),
                        purchaseDetails.productId == pendingTxnInfo.productIdentifier,
                        !ENCache.shared.isPaidUser {
                        
                        PaymentLogger.log(.purchasedTxnRecovery(subscriptionId: pendingTxnInfo.subscriptionId, productId: pendingTxnInfo.productIdentifier, purchaseDetails: purchaseDetails), context: "Purchased Txn")
                        
                        self.recoverFailedTxn { (isRecovered) in

                            // Always refresh user data after transaction attempt
                            self.refreshUserDataAfterPaymentInternal()

                            if isRecovered {
                                //Finish IAP transaction, if required
                                if purchaseDetails.needsFinishTransaction {
                                    purchaseDetails.finishTransactionHandler()
                                }
                            }
                        }
                        
                    } else {
                        
                        PaymentLogger.log(.purchasedTxnRestore(purchaseDetails: purchaseDetails), context: "Purchased Txn")
                        
                        self.restore(overVc: nil, isForceFul: false) { (isRestored) in
                            //Restored

                            // Always refresh user data after restore attempt
                            self.refreshUserDataAfterPaymentInternal()

                            //Finish IAP transaction, if required
                            if isRestored {
                                if purchaseDetails.needsFinishTransaction {
                                    purchaseDetails.finishTransactionHandler()
                                }
                            }
                        }
                    }
                default:
                    //Do nothing
                    //Payment Logger
                    break
                }
            })
            .initialize()
    }
    
    func fetchProductsFromStore(_ productIds: Set<ProductIdentifier>) -> Observable<RetrieveResults>  {
        return EasyIAP.rx.retrieveProductsInfo(productIds)
    }
    
    func makePayment(for product: SKProduct) -> Observable<PurchaseDetails> {
        
        //Whenver a payment attempt is made in the app, the auto restore mechanism should be activated back again.
        isAutoRestoreMechanismDisabled = false
        
        return EasyIAP.rx.makePayment(for: product, applicationUserName: ENCache.shared.userName, simulatesAskToBuyInSandbox: WILL_TEST_SANDBOX_ASK_TO_BUY, willFinishTxnImmeadiately: false)
    }
    
    func restore(overVc: UIViewController? = nil, isForceFul: Bool = true, completion: @escaping (Bool) -> Void) {
        
        restoreQueue.async {
            
            if isForceFul {
                SVProgressHUD.showENSVProgressLoader(withStatus: .clear, status: "Restoring...")
            } else {
                //Strictly skip restoring only for AUTOMATED restore mechanism
               if self.isAutoRestoreMechanismDisabled {
                    completion(true)
                    return
                }
            }
            
            self.restorePurchase(isForceFull: isForceFul)
                .observe(on: MainScheduler.instance)
                .subscribe(onNext: {[weak self] (subscription: SubscriptionState) in

                    if isForceFul { SVProgressHUD.dismiss() }

                    guard let self = self else { return }

                    // Refresh user data after successful restore
                    self.refreshUserDataAfterPaymentInternal()

                    self.setupActiveProducts()
                    self.presentRestoreError(message: Strings.Payment.productRestored, overVc: overVc)
                    if !isForceFul {
                        self.isAutoRestoreMechanismDisabled = true
                    }

                    PaymentLogger.log(.transactionRestored(info: "Subscription ID: \(subscription.subscriptionId)"), context: "Restore Purchase")

                    completion(true)
                    self.restoreSemaphor.signal()

                    }, onError: {[weak self] (error) in
                        
                        guard let self = self else { return }
                        
                        if isForceFul { SVProgressHUD.dismiss() }

                        // Refresh user data even on restore failure to get latest state
                        self.refreshUserDataAfterPaymentInternal()

                        if let error = error as? AppError {
                            if case AppError.paymentError(.receiptUnavailable) = error {
                                //User cancelled the Apple Popup. Don't show any error popup
                                //May be, receipt fetch error //Silently ignore the error
                            } else if
                                ((error.errorInfo.apiStatusCode == APIResponseStatusCode.subscriptionAlreadyExists &&
                                    !ENCache.shared.isPaidUser) ||
                                    error.errorInfo.apiStatusCode == APIResponseStatusCode.receiptIsInvalid1 ||
                                    error.errorInfo.apiStatusCode == APIResponseStatusCode.receiptIsInvalid2 ||
                                    error.errorInfo.apiStatusCode == APIResponseStatusCode.receiptIsInvalid3 ||
                                    error.errorInfo.apiStatusCode == APIResponseStatusCode.receiptIsInvalid4) {
                                
                                if isForceFul {
                                    self.presentRestoreError(message: Strings.Payment.productRestoreError, overVc: overVc)
                                } else {
                                    self.isAutoRestoreMechanismDisabled = true
                                }
                                self.setupActiveProducts()
                                PaymentLogger.log(.restoreReceiptDiscarded(info: error.errorInfo.apiStatusCode?.rawValue ?? "") ,context: "Restore Purchase")
                                completion(true)
                                self.restoreSemaphor.signal()
                                return
                            } else if case AppError.paymentError( _) = error {
                                if isForceFul {
                                    self.presentRestoreError(message: Strings.Payment.productRestoreError, overVc: overVc)
                                } else {
                                    self.isAutoRestoreMechanismDisabled = true
                                }
                                //If server is repeatedly giving error, then ignore this receipt and finish all transactions in the queue
                                PaymentLogger.log(.paymentFlow(error: error), context: "Restore Purchase")
                                completion(true)
                                self.restoreSemaphor.signal()
                                return
                            } else {
                                if isForceFul { self.presentRestoreError(message: Strings.Payment.productRestoreError, overVc: overVc) }
                            }
                            PaymentLogger.log(.paymentFlow(error: error), context: "Restore Purchase")
                        }
                        
                        completion(false)
                        self.restoreSemaphor.signal()
                })
                .disposed(by: self.disposeBag)
            self.restoreSemaphor.wait()
        }
    }
    
    func getReceipt() -> Observable<String> {
        return EasyIAP.rx.retrieveReceipt(with: .onlyFetch)
    }
    
    func recoverFailedTxn(completion: ((Bool)->Void)? = nil) {
        
        guard let pendingTxnInfo = getPendingTxnInfo(),
            !ENCache.shared.isPaidUser else {
                return
        }
        
        //There was a failed transaction and the user is still in BASIC plan
        
        EasyIAP.rx.retrieveReceipt(with: .onlyFetch)
            .flatMap {[weak self] (itunesReceipt) -> Observable<(String, RetrieveResults)> in
                guard let self = self else {return .empty()}
                return self.fetchProductsFromStore([pendingTxnInfo.productIdentifier])
                    .map { return (itunesReceipt, $0) }
                
        }
        .flatMap { (result: (ItunesReceipt, RetrieveResults)) -> Observable<([SubscriptionState])> in
            
            guard let skProduct = result.1.retrievedProducts?.first else {
                //The SKProduct involved in the failed/incomplete transaction is not a valid iTunes Product anymore
                //This plan must have got deprecated in Eros Now ecosystem
                throw AppError.restoreError(.noSkProductFound(ErrorInfo(statusCode: 1401)))
            }
            
            return self.createSubsccription(for: pendingTxnInfo.subscriptionId, itunesReceipt: result.0, skProduct: skProduct, country: ENCache.shared.countryCode, paymentTypeId: pendingTxnInfo.paymentTypeId)
                .catch { (error) -> Observable<[SubscriptionState]> in
                    throw AppError.paymentError(.subscriptionNotActivated(ErrorInfo(statusCode: 1306, errorMessage: "Network Error: \(error.localizedDescription)")))
                }
        }
        .flatMap({ (subscriptionStates: [SubscriptionState]) -> Observable<SubscriptionState> in
            
            guard let subscriptionState = subscriptionStates.first,
                subscriptionState.subscriptionStatus == .active else {
                    //No active subscription returned from the server
                    //Activate grace period if applicable
                    throw AppError.paymentError(.subscriptionNotActivated(ErrorInfo(statusCode: 1306,  errorMessage: "Server couldn't active the subscription. Subscription ID: \(subscriptionStates.first?.subscriptionId ?? 0)")))
            }
            
            Logging.log("EN IAP: Filter subscription state - \(subscriptionState)")
            return Observable.just(subscriptionState)
        })
            .applyDefaultSchedulers()
            .subscribe(onNext: {[weak self] (subscriptionState: SubscriptionState) in
                guard let self = self else {return}
                self.presentRestoreError(message: Strings.Payment.failedTxnRecovered)
                self.setupActiveProducts()
                PaymentLogger.log(.transactionRecovered(info: "Subscription ID: \(subscriptionState.subscriptionId)"), context: "Purchased Txn")
                
                //Once the receipt is sent to the server for any incomplete/failed transaction then remove the cached info
                self.resetPendingTxnInfo()
                completion?(true)
                
                }, onError: {[weak self] (error) in
                    guard let self = self else {return}

                    if let appError = error as? AppError {
                        
                        if case AppError.paymentError(.subscriptionNotActivated) = appError {
                            //Once the receipt is sent to the server for any incomplete/failed transaction and then even if the server responds with error,  remove the cached info. User will restore via RESTORE PURCHASE flow.
                            self.resetPendingTxnInfo()
                        }
                        
                        PaymentLogger.log(.paymentFlow(error: appError), context: "Purchased Txn")
                    }
                    completion?(false)
            })
            .disposed(by: disposeBag)
        
    }
    
    //MARK:- Utility methods
    
    func merge(_ storeProducts: RetrieveResults, and subscriptionResponse: PlanData) -> [SubscriptionType]? {
        
        var subscriptionTypes = [SubscriptionType]()
        
        for enPlanGroup in subscriptionResponse.result {
            let groupTitle = enPlanGroup.entitlement
            
            if let entitlementGroup = EntitlementGroup(rawValue: enPlanGroup.entitlementGroup ?? "") {
                switch entitlementGroup {
                case .avod:
                    let group = SubscriptionGroup(plans: enPlanGroup.productPlans, title: "", actionButton: "")
                    subscriptionTypes.append(.avod(group: group))
                    
                case .tvod, .svod:
                    var iapPlans = [SubscriptionPlan]()
                    for enPlan in enPlanGroup.productPlans {
                        if let skProduct = storeProducts.retrievedProducts?.first(where: { (skProduct: SKProduct) -> Bool in
                            return skProduct.productIdentifier == enPlan.iapProductId
                        }) {
                            let plan = SubscriptionPlan(enPlan: enPlan, skProduct: skProduct)
                            iapPlans.append(plan)
                        }
                    }
                    
                    if entitlementGroup == .tvod {
                        let group = SubscriptionGroup(plans: iapPlans, title: groupTitle, actionButton: "More Info")
                        subscriptionTypes.append(.tvod(group: group))
                    } else {
                        let group = SubscriptionGroup(plans: iapPlans, title: groupTitle, actionButton: "See Benefits")
                        subscriptionTypes.append(.svod(group: group))
                    }
                }
            }
            
        }

        // Debug logging for final result
        Logging.log("EN IAP: Final subscription types count: \(subscriptionTypes.count)")
        for (index, type) in subscriptionTypes.enumerated() {
            switch type {
            case .svod(let group):
                Logging.log("EN IAP: SVOD group \(index) has \(group.plans.count) plans")
                for plan in group.plans {
                    Logging.log("EN IAP: - Plan: \(plan.enPlan.planDisplayName) (\(plan.skProduct.productIdentifier))")
                }
            case .tvod(let group):
                Logging.log("EN IAP: TVOD group \(index) has \(group.plans.count) plans")
            case .avod(let group):
                Logging.log("EN IAP: AVOD group \(index) has \(group.plans.count) plans")
            }
        }

        return subscriptionTypes.isEmpty ? nil : subscriptionTypes

    }
    
    func getIAPProductIds(from enResponse: PlanData) -> Set<ProductIdentifier>? {

        var productIds = Set<ProductIdentifier>()

        let planGroups = enResponse.result.filter { (group) -> Bool in
            return group.entitlementGroup == "SVOD"
        }

        for group in planGroups {
            for plan in group.productPlans {
                if let iapProductId = plan.iapProductId {
                    productIds.insert(iapProductId)
                    Logging.log("EN IAP: Requesting product ID: \(iapProductId) for plan: \(plan.planDisplayName)")
                }
            }
        }

        // Debug logging for all requested product IDs
        Logging.log("EN IAP: Total product IDs requested from StoreKit: \(productIds)")

        return productIds.isEmpty ? nil : productIds
    }
    
    
    //MARK:- Pending Txn Info
    
    func savePendingTxnInfo(pendingSubscriptionId: String, pendingProductIdentifier: String, paymentTypeId: PaymentTypeId) {
        userDefaultService.set(pendingSubscriptionId, forKey: .pendingPaymentSubscriptionId)
        userDefaultService.set(pendingProductIdentifier, forKey: .pendingPaymentProductIdentifier)
        userDefaultService.set(paymentTypeId, forKey: .pendingPaymentTypeId)
    }
    
    private func getPendingTxnInfo() -> (subscriptionId: String, productIdentifier: String, paymentTypeId: PaymentTypeId)? {
        guard let pendingSubscriptionId = userDefaultService.value(forKey: .pendingPaymentSubscriptionId, type: String.self),
            let pendingProductIdentifier = userDefaultService.value(forKey: .pendingPaymentProductIdentifier, type: String.self),
            let pendingPaymentPaymentTypeId = userDefaultService.value(forKey: .pendingPaymentTypeId, type: PaymentTypeId.self) else {
                return nil
        }
        return (pendingSubscriptionId, pendingProductIdentifier, pendingPaymentPaymentTypeId)
    }
    
    func resetPendingTxnInfo() {
        userDefaultService.removeObject(forKey: .pendingPaymentSubscriptionId, type: String.self)
        userDefaultService.removeObject(forKey: .pendingPaymentProductIdentifier, type: String.self)
        userDefaultService.removeObject(forKey: .pendingPaymentTypeId, type: PaymentTypeId.self)
    }
    
    //MARK: - Active Products

    private func setupActiveProducts() {
        guard ENCache.shared.isLoggedIn else {
            return
        }
        accountService
            .getActiveProducts()
            .applyDefaultSchedulers()
            .subscribe(onNext: { [accountService] (activeProduct) in
                accountService.saveUserAndAccountInfo(type: nil, info: nil, activeProduct: activeProduct)
            })
            .disposed(by: disposeBag)
    }

    //MARK: - User Data Refresh After Payment

    @objc func refreshUserDataAfterPayment() {
        refreshUserDataAfterPaymentInternal()
    }

    /// Refreshes user data after any payment attempt (success or failure)
    private func refreshUserDataAfterPaymentInternal() {
        guard ENCache.shared.isLoggedIn else {
            return
        }

        // Small delay to allow server-side processing
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
            guard let self = self else { return }

            self.accountService
                .getActiveProducts()
                .retry(2)
                .applyDefaultSchedulers()
                .subscribe(onNext: { [accountService] (activeProduct) in
                    accountService.saveUserAndAccountInfo(type: nil, info: nil, activeProduct: activeProduct)
                    Logging.log("User data refreshed after payment - subscription status updated")
                    NotificationCenter.default.post(name: Notification.Name(kUserDidChangeNotification), object: nil)
                }, onError: { (error) in
                    if let appError = error as? AppError {
                        PaymentLogger.log(.paymentFlow(error: appError), context: "Failed to refresh user data after payment")
                    } else {
                        Logging.log("Failed to refresh user data after payment: \(error.localizedDescription)")
                    }
                })
                .disposed(by: self.disposeBag)
        }
    }
    
    //MARK:- Error UI
    
    private func presentRestoreError(message: String, overVc: UIViewController? = nil) {
        
        let alert = AlertController(title: Strings.Payment.restoreTitle, message: message, preferredStyle: .alert)
        alert.addAction(AlertAction(title: Strings.ok, style: .default, handler: { _ in
            //nothing to do
        }))
        
        if let vc = overVc {
            vc.present(alert, animated: true, completion: nil)
        } else {
            UIApplication.topViewController()?.present(alert, animated: true, completion: nil)
        }
    }
}

extension IAPService: ENPaymentProcessor {
    
    private func restorePurchase(isForceFull: Bool) -> Observable<SubscriptionState> {
        
        EasyIAP.rx.retrieveReceipt(with: isForceFull ? .alwaysRefresh : .onlyFetch)
            .applyDefaultBackgroundSchedulers()
            .catch { (error) -> Observable<String> in
                throw AppError.paymentError(.receiptUnavailable(ErrorInfo(statusCode: 1304)))
            }
            .flatMap {[weak self] (iTunesReceipt: String) -> Observable<SubscriptionState> in
                guard let self = self else { return Observable.empty() }
                return self.restore(iTunesReceipt: iTunesReceipt)
                    .catch { (error) -> Observable<SubscriptionState> in
                        throw AppError.paymentError(.subscriptionNotActivated(ErrorInfo(statusCode: 1306, errorMessage: "Network Error")))
                }
        }
        .flatMap({ (subscriptionState: SubscriptionState) -> Observable<SubscriptionState> in
            guard subscriptionState.subscriptionStatus == .active else {
                //No active subscription returned from the server
                throw AppError.paymentError(.subscriptionNotActivated(ErrorInfo(statusCode: 1306,  errorMessage: "Server couldn't active the subscription during restore. Subscription ID: \(subscriptionState.subscriptionId)")))
            }
            return Observable.just(subscriptionState)
        })
        
    }
}
