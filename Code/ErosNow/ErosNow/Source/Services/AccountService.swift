//
//  AccountService.swift
//  ErosNow
//
//  Created by <PERSON><PERSON><PERSON> on 12/09/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import Foundation
import RxSwift
//import CoreData

final class AccountService {
    
    private let userDefaultService: UserDefaultsService
    private let avodService: AVoDService
    
    private var networking: NetworkService
    private let disposeBag = DisposeBag()
    
    init(networking: NetworkService, userDefaultService: UserDefaultsService, avodService: AVoDService) {
        self.networking = networking
        self.userDefaultService = userDefaultService
        self.avodService = avodService
    }
    
    /// Fetches active products under the user's account from the server and saves them locally
    ///
    /// - Returns: Observable ActiveProduct
    func getActiveProducts() -> Observable<ActiveProduct>  {
        return networking
            .rxRequest(AccountAPI.activeProduct)
    }
    
    /// Fetches User related info
    ///
    /// - Returns: Observable UserInfo object
    func getUserInfo() -> Observable<UserInfo> {
        return self.networking
            .rxRequest(AccountAPI.account)
    }
    
    /// Saves the User info and Account Info received
    ///
    /// - Parameters:
    ///   - type: The login method used by the user
    ///   - info: UserInfo object
    ///   - activeProduct: ActiveProduct object
    func saveUserAndAccountInfo(type: UserLoginType?, info: UserInfo?, activeProduct: ActiveProduct?) {
        
        if let info = info {
            saveUserInfo(type: type, info: info)
        }
        
        if let activeProduct = activeProduct {
            saveAccountInfo(activeProduct)
        }
        
        //Analytics User identification
        AnalyticsManager.shared.identifyErosUser(user: ENDataManager.shared.currentUser, activeProduct: activeProduct)
        
        //Analytics login
        if let uuid = activeProduct?.uuid{
            AnalyticsManager.shared.login(uuid: uuid)
        }
    }
    
    
    /// Saves the User info received
    ///
    /// - Parameters:
    ///   - type: The login method used by the user
    ///   - info: UserInfo object
    private func saveUserInfo(type: UserLoginType?, info: UserInfo) {
        guard  let profile = info.profile
            else {
                return
        }
        let currentUser = ENDataManager.shared.currentUser
        if let callingCode = profile.callingCode {
            if callingCode.contains("+") {
                currentUser.callingCode = callingCode
            } else {
                currentUser.callingCode = "+\(callingCode)"
            }
        }
        //Country code to be updated only from the country API
        // currentUser.countryCode = profile.countryCode
        // currentUser.isNRI = profile.isNRI
        currentUser.firstName = profile.firstName
        currentUser.username = profile.username
        currentUser.lastName = profile.lastName
        currentUser.aboutMe = profile.about
        currentUser.city = profile.city
        currentUser.country = profile.country
        currentUser.email = profile.email
        currentUser.gender = profile.gender
        currentUser.image = profile.image
        currentUser.dateOfBirth = profile.dob
        
        switch type {
        case .email(let emailId):
            currentUser.loginType = NSNumber(value: ENUserLoginTypeEmail.rawValue)
            currentUser.email = emailId
        case .mobile(let mobileNo, let countryCode):
            currentUser.loginType = NSNumber(value: ENUserLoginTypeMobileNo.rawValue)
            currentUser.mobile = mobileNo
            currentUser.callingCode = countryCode
        case .socialLogin(let socialToken, _):
            currentUser.loginType = NSNumber(value: ENUserLoginTypeFacebookNative.rawValue)
            currentUser.facebookUserId = socialToken
        default:
            break
        }
        
        ENDataManager.shared.saveDataInManagedContext({ (saved, error) in })
    }
    
    /// Saves Active Product locally
    ///
    /// - Parameter activeProduct: ActiveProduct object
    private func saveAccountInfo(_ activeProduct: ActiveProduct) {
        guard let activeProductDict = activeProduct.asDictionary(keyEncodingStrategy: .convertToSnakeCase)
        else { return }
        let currentUser = ENDataManager.shared.currentUser
        //Update Current user
        currentUser.userType = NSNumber(value: ENUser.getTypeFromProductDetails(activeProductDict as Dictionary<String, AnyObject>).rawValue)
        currentUser.paymentType = activeProduct.paymentMethod
        
        //Update current user using legacy code
        ENUserFeatureSet.parseFeatureSetInformation(activeProductDict as Dictionary<String, AnyObject>)
        
        //Universal Search Registration
        ENAccountUtility.store(subscription: activeProduct.productName ?? "", nextBillingDate: activeProduct.nextBillingDate)
//        ENUniversalSearchUtility.updateSuscriptionInfo()
        //Universal Search Registration--------end
        
        //Post notification so that different rendered list gets refreshed
        NotificationCenter.default.post(name: Notification.Name(kUserDidChangeNotification), object: nil)

        // Post additional notification for subscription status changes
        NotificationCenter.default.post(name: Notification.Name("ENSubscriptionStatusDidChange"), object: activeProduct)

        //Save to coredata
        ENDataManager.shared.saveDataInManagedContext({ (saved, error) in })
    }
    
    func setupMzaaloAndLogin(isRegistrationAvod: Bool = false) {
        guard ENCache.shared.isLoggedIn, ENCache.shared.isAVoDUser else {
            return
        }
        avodService.setup { (success, error) in
            if success, let userId = ENDataManager.shared.currentUser.uuid {
                self.avodService.login(userId: userId) { (success, error) in
                    if success {
                        ENCache.shared.isMzaaloLoginSuccessful = true
                        if isRegistrationAvod {
                            self.avodService.registerRewardAction(type: .signedUp)
                        } else {
                            self.avodService.registerRewardAction(type: .checkedIn)
                        }
                    }
                }
            }
        }
    }
}
