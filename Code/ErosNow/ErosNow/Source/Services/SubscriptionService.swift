//
//  SubscriptionService.swift
//  ErosNow
//
//  Created by <PERSON><PERSON><PERSON> on 07/08/20.
//  Copyright © 2020 ErosNow. All rights reserved.
//

import Foundation
import RxSwift

protocol SubscriptionDelegate: AnyObject {
    func subscritionToAVODSuccess(completion: LoginCompletion)
    func subscritionToAVODFailed(error: Error)
    func subcribeToSVOD(completion: @escaping LoginCompletion, analyticsMeta: [AnalyticsProperty: Any])
}

final class AVODPaymentService {
    
    private let accountService: AccountService
    private var networking: NetworkService
    
    init(networking: NetworkService, accountService: AccountService) {
        self.networking = networking
        self.accountService = accountService
    }
    
    public func getAvodSubscription(metadata: [AnalyticsProperty: Any]) -> Observable<AVODSubscriptionState> {
        Logging.log("EN AVOD: Creating AVOD subscription")

        return networking
            .rxRequest(SubscriptionAPI.createAVODSubscription, objectType: [AVODSubscriptionState].self)
            .timeout(Constants.SubscriptionTimeout.createSubscription, scheduler: MainScheduler.instance)
            .retry(2) // Retry twice for AVOD subscription creation endpoint
            .do(onNext: { subscriptionStates in
                Logging.log("EN AVOD: AVOD subscription creation API successful")
            }, onError: { error in
                if error is RxError {
                    Logging.log("EN AVOD: AVOD subscription creation timed out after 120 seconds")
                } else {
                    Logging.log("EN AVOD: AVOD subscription creation failed after retries - Error: \(error)")
                }
            })
            .flatMap { (subscriptionStates) -> Observable<AVODSubscriptionState> in
                guard let subState = subscriptionStates.first, subState.subscriptionStatus == .active else {
                        //No active subscription returned from the server
                        throw AppError.paymentError(.subscriptionNotActivated(ErrorInfo(statusCode: 1306,  errorMessage: "Server couldn't active the subscription.")))
                }
                return Observable.just(subState)
            }
            .do(onNext: { (subState) in
                AnalyticsManager.shared.track(event: .avodSuccess, metadata: metadata)
            })
            .flatMap({ (subState) -> Observable<(AVODSubscriptionState, ActiveProduct)> in
                return Observable.zip(Observable.just(subState), self.accountService.getActiveProducts())
            })
            .observe(on: MainScheduler.instance)
            .flatMap({ [accountService] (result: (subState: AVODSubscriptionState, activeProduct: ActiveProduct)) -> Observable<AVODSubscriptionState> in
                accountService.saveUserAndAccountInfo(type: nil, info: nil, activeProduct: result.activeProduct)
                self.setupMzaaloAndLogin()
                self.showAvodSubSuccessMessage()
                return Observable.just(result.subState)
            })
    }
    
    private func showAvodSubSuccessMessage() {
        SnackBar(message: "Subscription successful", type: .success, position: .bottom, duration: .medium).show()
    }
    
    func setupMzaaloAndLogin() {
        // isRegistrationAvod is true as user Activated AVoD
        accountService.setupMzaaloAndLogin(isRegistrationAvod: true)
    }
}

final class SubscriptionAlertCordinator: NSObject {
    
    @objc static let shared = SubscriptionAlertCordinator()
    private let disposeBag = DisposeBag()
    private let avodService: AVODPaymentService
    
    private weak var delegate: SubscriptionDelegate?
    private var loginCompletion: LoginCompletion?
    private var type: SubscriptionPopupType?
    private var meta: [AnalyticsProperty: Any] = [:]
    
    private override init() {
        let networking = Networking.shared
        let accountService = AccountService(networking: networking, userDefaultService: UserDefaultsService(), avodService: MzaaloService(networking: Networking.mzaaloService))
        avodService = AVODPaymentService(networking: networking, accountService: accountService)
    }
    
    private var topViewController: UIViewController? {
        return UIApplication.shared.keyWindow?.rootViewController
    }
    
    func showAVODSubscription(meta: [AnalyticsProperty: Any], delegate: SubscriptionDelegate, loginCompletion: @escaping LoginCompletion) {
        showSubscription(type: .avod, meta: meta, delegate: delegate, loginCompletion: loginCompletion)
    }
    
    func showAvodSvodSubscription(meta: [AnalyticsProperty: Any], delegate: SubscriptionDelegate, loginCompletion: @escaping LoginCompletion) {
        showSubscription(type: .avodAndSvod, meta: meta, delegate: delegate, loginCompletion: loginCompletion)
    }
    
    private func showSubscription(type: SubscriptionPopupType, meta: [AnalyticsProperty: Any],  delegate: SubscriptionDelegate, loginCompletion: @escaping LoginCompletion) {
        self.delegate = delegate
        self.loginCompletion = loginCompletion
        self.type = type
        self.meta = meta
        let vc = SubscriptionPopupViewController(type: type, meta: meta, delegate: self, isLoggedIn: ENCache.shared.isLoggedIn)
        topViewController?.present(vc, animated: true, completion: nil)
    }
    
    private func showSubscriptionAgain() {
        guard let _type = self.type, let _delegate = self.delegate, let completion = self.loginCompletion else {
            return
        }
        showSubscription(type: _type, meta: self.meta, delegate: _delegate, loginCompletion: completion)
    }
    
    private func confirmAVODSubscription() {
        let alert = AlertController(title: "", message: "Are you sure you want to subscribe to Mzaalo plan?", preferredStyle: .alert)
        
        alert.addAction(AlertAction(title: "YES", style: .default, handler: { (action) in
            self.subscribeToAVODPlan()
        }))
        
        alert.addAction(AlertAction(title: "NO", style: .default, handler: { (action) in
            
        }))
        
        topViewController?.present(alert, animated: true, completion: nil)
    }
    
    private func subscribeToAVODPlan() {
        SVProgressHUD.showENSVProgressLoader(withStatus: .clear, status: "Processing...")
        var metadata: [AnalyticsProperty: Any] = [
            AnalyticsProperty.screenName: topViewController?.screenName ?? ""
        ]
        metadata.merge(self.meta) { (current, _) in
            return current
        }
        self.avodService.getAvodSubscription(metadata: metadata)
            .applyDefaultSchedulers()
            .subscribe(onNext: { (subState) in
                Logging.log("EN AVOD: onNext - \(subState.subscriptionStatus)")
            }, onError: { (error) in
                Logging.log("EN AVOD: onError - \(error)")
                SVProgressHUD.dismiss()
                
                guard let appError = error as? AppError else {
                    self.delegate?.subscritionToAVODFailed(error: error)
                    return
                }
                
                if appError.errorInfo.apiStatusCode == .alreadySubscribed {
                    self.avodSubscriptionSuccess()
                    return
                }
                
                self.delegate?.subscritionToAVODFailed(error: error)
            }, onCompleted: {
                Logging.log("EN AVOD: onCompleted")
                SVProgressHUD.dismiss()
                self.avodSubscriptionSuccess()
            })
            .disposed(by: disposeBag)
    }
    
    private func avodSubscriptionSuccess() {
        guard let completion = self.loginCompletion else {
            return
        }
        delegate?.subscritionToAVODSuccess(completion: completion)
    }
    
    private func handleForceLogin(isLoggedIn: Bool, isSubscribed: Bool) {
        if !isLoggedIn {
            return // User skipped the login no need to complete the action
        }
        
        if (ENCache.shared.isAVoDUser || isSubscribed) {
            finishWithLoginCompletion() // Already Subcribed to watch (AVoD, AVoD+SVoD)
        } else {
            self.showSubscriptionAgain() // Show Alert again, to take user action
        }
    }
    
    private func finishWithLoginCompletion() {
        self.loginCompletion?(
            ENCache.shared.isLoggedIn,
            ENCache.shared.isPaidUser
        )
    }
    
    private func handleAVODForceLogin(isLoggedIn: Bool, isSubscribed: Bool) {
        if !isLoggedIn {
            return // User skipped the login
        }
        
        if (ENCache.shared.isAVoDUser || isSubscribed) {
            finishWithLoginCompletion() // Already Subcribed to watch
        } else {
            self.confirmAVODSubscription()
        }
    }
    
}

extension SubscriptionAlertCordinator: SubscriptionPopupVCDelegate {
    
    func subscribeToAVOD() {
        if ENCache.shared.isLoggedIn {
            confirmAVODSubscription()
        } else {
            guard let topVC = self.topViewController else {
                return
            }
            ForceLoginCordinator.shared.presentForceLogin(from: topVC, analyticsMeta: self.meta) { [weak self] (isLoggedIn, isSubscribed) in
                self?.handleAVODForceLogin(isLoggedIn: isLoggedIn, isSubscribed: isSubscribed)
            }
        }
    }
    
    
    func subscribeToSVOD() {
        guard let completion = self.loginCompletion else {
            return
        }
        delegate?.subcribeToSVOD(completion: completion, analyticsMeta: self.meta)
    }
    
    func signInToSubscribe() {
        guard let topVC = self.topViewController else {
            return
        }
        ForceLoginCordinator.shared.presentForceLogin(from: topVC, analyticsMeta: self.meta) { [weak self] (isLoggedIn, isSubscribed) in
            self?.handleForceLogin(isLoggedIn: isLoggedIn, isSubscribed: isSubscribed)
        }
    }
}

extension UIViewController: SubscriptionDelegate {
    
    func subscritionToAVODSuccess(completion: LoginCompletion) {
        completion(
            ENCache.shared.isLoggedIn,
            ENCache.shared.isPaidUser
        )
    }
    
    func subscritionToAVODFailed(error: Error) {
        SnackBar(message: error.localizedDescription, type: .failure).show()
    }
    
    func subcribeToSVOD(completion: @escaping LoginCompletion, analyticsMeta: [AnalyticsProperty: Any]) {
        ForceLoginCordinator.shared.presentForceSubscribeLogin(from: self, analyticsMeta: analyticsMeta, completion: completion, entitlementGroups: [.svod, .avod])
    }
    
}
