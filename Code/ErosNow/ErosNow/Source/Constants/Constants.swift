//
//  Constants.swift
//  ErosNow
//
//  Created by <PERSON><PERSON> on 16/07/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import Foundation
import RxSwift

/// Constants enum used for numeric/default constants like width, size, padding etc. Grouping should be used for specific contexts like given in below example
/// eg.
/// enum Constants {
///     enum Screen {
///         static let width = UIScreen.main.bounds.width
///         static let height = UIScreen.main.bounds.height
///     }
/// }
///
///

enum Constants {
    
    static let retryCount = 2
    static let defaultCallingCode = "+91"
    
    enum Config {
        static let privacyPolicyUrl = "https://test.erosnow.com/mobile_privacy_policy"
        static let termsAndConditionsUrl = "https://test.erosnow.com/mobile_terms_of_use"
        static let faqUrl = "https://test.erosnow.com/mobile_faq"
        static let youtubeOfferUrl = "https://erosnow.com/offer/youtube"
        
        static let appLinkScheme = "erosnowapp://"
    }
    
    enum Timer {
        static let RESEND_OTP: TimeInterval = 30
    }

    enum Payment {
        // Individual payment operation timeout (reduced from 60s network timeout)
        static let operationTimeout: RxTimeInterval = .seconds(25)
        // Total payment flow timeout (for entire subscription process)
        static let totalFlowTimeout: RxTimeInterval = .seconds(90)
        // IAP payment timeout (Apple's payment dialog)
        static let iapTimeout: RxTimeInterval = .seconds(120)
    }
    
    enum Screen {
        static let width: CGFloat = UIScreen.main.bounds.width
        static let height: CGFloat = UIScreen.main.bounds.height
    }
    
    enum Storyboard {
        static let main = "Main"
    }
    
    enum AppStore {
         static let appStoreUrl = "itms-apps://itunes.apple.com/app/id551666302" //itms-apps://geo.itunes.apple.com/in/app/eros-now/id551666302?mt=8
    }
    
    enum JWTToken {
        static let safeRfreshWindow = 10
    }
    
    enum Mzaalo {
        static let partnerCode = "eros"
        static let shareLink = "https://share.mzaalo.com/mobile-app"
        static let termsLink = "https://erosnow.com/help_mzaalo?section=mzaaloTerms"
    }
}
