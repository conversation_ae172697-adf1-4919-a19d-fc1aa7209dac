//
//  RxSwift+Extensions.swift
//  ErosNow
//
//  Created by <PERSON><PERSON>il on 10/09/19.
//  Copyright © 2019 ErosNow. All rights reserved.
//

import RxSwift

extension ObservableType {
    
    func mapTo<Result>(_ value: Result) -> Observable<Result> {
        return map { _ in value }
    }
    
}

extension ObservableType {
    
    func applyDefaultSchedulers() -> Observable<Element> {
        return
            subscribe(on: ConcurrentDispatchQueueScheduler(qos: .default))
            .observe(on: MainScheduler.instance)
    }
    
    func applyDefaultBackgroundSchedulers() -> Observable<Element> {
        return
            subscribe(on: ConcurrentDispatchQueueScheduler(qos: .default))
            .observe(on: ConcurrentDispatchQueueScheduler(qos: .default))
    }
    
    func applyUserInitiatedSchedulers() -> Observable<Element> {
        return
            subscribe(on: ConcurrentDispatchQueueScheduler(qos: .userInitiated))
            .observe(on: ConcurrentDispatchQueueScheduler(qos: .userInitiated))
    }
    
    func applySerialSchedulers() -> Observable<Element> {
        return
            subscribe(on: SerialDispatchQueueScheduler(qos: .default))
            .observe(on: SerialDispatchQueueScheduler(qos: .default))
    }
    
    func applyCustomSchedulers(subscribeOn subscriptionScheduler: ConcurrentDispatchQueueScheduler, observeOn observerScheduler: ConcurrentDispatchQueueScheduler) -> Observable<Element> {
        return
            subscribe(on: subscriptionScheduler)
            .observe(on: observerScheduler)
    }
    
    func logError() -> Observable<Element> {
        return self.do(onError: { error in
            Logging.log(error.localizedDescription)
        })
    }
    
    func observeOnMainScheduler() -> Observable<Element> {
        return observe(on: MainScheduler.instance)
    }
    
    func subscriveOnScheduler(_ scheduler: DispatchQoS) -> Observable<Element> {
        return subscribe(on: ConcurrentDispatchQueueScheduler(qos: scheduler))
    }

    /// Apply timeout with proper error handling for payment flows
    func timeoutForPayment(_ timeout: RxTimeInterval, scheduler: SchedulerType = MainScheduler.instance) -> Observable<Element> {
        return self.timeout(timeout, scheduler: scheduler)
            .catch { error in
                if error is RxError {
                    // Convert RxError.timeout to AppError for consistent error handling
                    let timeoutError = AppError.networkError(.timeout(ErrorInfo(statusCode: 408, errorMessage: "Payment operation timed out. Please try again.")))
                    return Observable.error(timeoutError)
                }
                return Observable.error(error)
            }
    }
}
