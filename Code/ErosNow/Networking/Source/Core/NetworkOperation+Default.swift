

//
//  NetworkOperation_Default.swift
//  NetworkLayer
//
//  Created by <PERSON><PERSON> on 15/05/19.
//

import Foundation


extension NetworkOperation {
    
    public static func defaultEndpointMapping(for endPoint: EndPoint) -> NetworkEndPoint {
        return NetworkEndPoint(
            url: URL(endPoint: endPoint).absoluteString,
            method: endPoint.method,
            task: endPoint.task,
            httpHeaders: endPoint.headers
        )
    }
    
    public static func defaultSessionConfiguration() -> URLSessionConfiguration {
        let configuration = URLSessionConfiguration.default
        configuration.httpAdditionalHeaders = NetworkOperation.additionalHeaders()
        if #available(iOS 11.0, tvOS 11.0, *) {
            configuration.waitsForConnectivity = true
        }
        // Reduced timeouts for better payment flow performance
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 45
        return configuration
    }
    
    public static func additionalHeaders() ->  [AnyHashable: Any]? {
        return [
            "Accept": "application/json",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "en-us",
            "CONNECTION": "keep-alive"
        ]
    }
}
